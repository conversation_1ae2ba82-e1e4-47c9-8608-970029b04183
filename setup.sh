#!/bin/bash

echo "=== Vulkan Triangle Setup for macOS ARM64 ==="
echo ""

# Проверка Homebrew
if ! command -v brew &> /dev/null; then
    echo "Homebrew не установлен. Установка..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
fi

# Установка зависимостей
echo "Установка зависимостей..."

# CMake
if ! command -v cmake &> /dev/null; then
    echo "Установка CMake..."
    brew install cmake
else
    echo "CMake уже установлен"
fi

# GLFW
echo "Установка GLFW..."
brew install glfw

# Vulkan SDK
echo "Установка Vulkan SDK..."
echo "Vulkan SDK нужно скачать с официального сайта"
echo "Открываю страницу загрузки..."
open "https://vulkan.lunarg.com/sdk/home#mac"
echo ""
echo "1. Скачайте Vulkan SDK для macOS"
echo "2. Запустите установщик .dmg"
echo "3. Следуйте инструкциям установщика"
echo ""
echo "Или установите через Homebrew Cask:"
brew install --cask vulkan-sdk

# Установка дополнительных компонентов Vulkan
echo "Установка дополнительных компонентов..."
brew install vulkan-headers vulkan-loader vulkan-tools

# Проверка установки
echo ""
echo "Проверка установки:"
echo -n "CMake: "
cmake --version | head -n 1

echo -n "GLFW: "
if brew list glfw &>/dev/null; then
    echo "установлен"
else
    echo "не установлен"
fi

echo -n "Vulkan SDK: "
if [ -d "/usr/local/Caskroom/vulkan-sdk" ] || [ -d "/opt/homebrew/Caskroom/vulkan-sdk" ]; then
    echo "установлен"
else
    echo "не установлен"
fi

# Установка переменных окружения
echo ""
echo "Настройка переменных окружения..."

# Определение архитектуры
if [[ $(uname -m) == 'arm64' ]]; then
    VULKAN_SDK_PATH="/opt/homebrew/Caskroom/vulkan-sdk/latest/macOS"
else
    VULKAN_SDK_PATH="/usr/local/Caskroom/vulkan-sdk/latest/macOS"
fi

# Экспорт переменных для текущей сессии
export VULKAN_SDK=$VULKAN_SDK_PATH
export PATH=$VULKAN_SDK/bin:$PATH
export DYLD_LIBRARY_PATH=$VULKAN_SDK/lib:$DYLD_LIBRARY_PATH
export VK_ICD_FILENAMES=$VULKAN_SDK/share/vulkan/icd.d/MoltenVK_icd.json
export VK_LAYER_PATH=$VULKAN_SDK/share/vulkan/explicit_layer.d

# Добавление в .zshrc для постоянного использования
echo ""
echo "Добавление переменных окружения в ~/.zshrc..."

cat >> ~/.zshrc << EOL

# Vulkan SDK
export VULKAN_SDK=$VULKAN_SDK_PATH
export PATH=\$VULKAN_SDK/bin:\$PATH
export DYLD_LIBRARY_PATH=\$VULKAN_SDK/lib:\$DYLD_LIBRARY_PATH
export VK_ICD_FILENAMES=\$VULKAN_SDK/share/vulkan/icd.d/MoltenVK_icd.json
export VK_LAYER_PATH=\$VULKAN_SDK/share/vulkan/explicit_layer.d
EOL

echo ""
echo "=== Установка завершена ==="
echo ""
echo "Для применения переменных окружения выполните:"
echo "source ~/.zshrc"
echo ""
echo "Или перезапустите терминал"