#!/bin/bash

echo "=== Установка Vulkan для macOS ARM64 ==="
echo ""

# Определение архитектуры
ARCH=$(uname -m)
if [[ "$ARCH" == "arm64" ]]; then
    echo "Обнаружен Apple Silicon (ARM64)"
    HOMEBREW_PREFIX="/opt/homebrew"
else
    echo "Обнаружен Intel Mac"
    HOMEBREW_PREFIX="/usr/local"
fi

# Проверка Homebrew
if ! command -v brew &> /dev/null; then
    echo "Homebrew не установлен. Установка..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
fi

echo ""
echo "=== Установка зависимостей ==="

# CMake
if ! command -v cmake &> /dev/null; then
    echo "Установка CMake..."
    brew install cmake
else
    echo "✓ CMake уже установлен"
fi

# GLFW
echo "Установка GLFW..."
brew install glfw

# Vulkan компоненты через Homebrew
echo ""
echo "Установка компонентов Vulkan..."
brew install vulkan-headers vulkan-loader vulkan-tools molten-vk

# Установка Vulkan SDK через Cask
echo ""
echo "Установка Vulkan SDK..."
brew install --cask vulkan-sdk

# Проверка установки
echo ""
echo "=== Проверка установки ==="

# Поиск Vulkan SDK
VULKAN_SDK_SEARCH_PATHS=(
    "$HOME/VulkanSDK/*/macOS"
    "/usr/local/VulkanSDK/*/macOS"
    "$HOMEBREW_PREFIX/Caskroom/vulkan-sdk/*/macOS"
)

VULKAN_SDK_FOUND=""
for path in "${VULKAN_SDK_SEARCH_PATHS[@]}"; do
    if compgen -G "$path" > /dev/null; then
        VULKAN_SDK_FOUND=$(ls -d $path 2>/dev/null | head -n 1)
        break
    fi
done

if [ -n "$VULKAN_SDK_FOUND" ]; then
    echo "✓ Vulkan SDK найден: $VULKAN_SDK_FOUND"
else
    echo "✗ Vulkan SDK не найден"
    echo ""
    echo "Попробуйте скачать и установить вручную:"
    echo "https://vulkan.lunarg.com/sdk/home#mac"
    exit 1
fi

# Настройка переменных окружения
echo ""
echo "=== Настройка переменных окружения ==="

# Создание скрипта настройки окружения
cat > setup-env.sh << EOL
#!/bin/bash
# Скрипт настройки окружения Vulkan

export VULKAN_SDK="$VULKAN_SDK_FOUND"
export PATH="\$VULKAN_SDK/bin:\$PATH"
export DYLD_LIBRARY_PATH="\$VULKAN_SDK/lib:\$DYLD_LIBRARY_PATH"
export VK_ICD_FILENAMES="\$VULKAN_SDK/share/vulkan/icd.d/MoltenVK_icd.json"
export VK_LAYER_PATH="\$VULKAN_SDK/share/vulkan/explicit_layer.d"

echo "Vulkan environment configured:"
echo "VULKAN_SDK = \$VULKAN_SDK"
EOL

chmod +x setup-env.sh

# Добавление в shell конфигурацию
SHELL_CONFIG="$HOME/.zshrc"
if [ -f "$HOME/.bash_profile" ]; then
    SHELL_CONFIG="$HOME/.bash_profile"
fi

echo ""
echo "Добавление в $SHELL_CONFIG..."

# Проверка, не добавлены ли уже переменные
if ! grep -q "VULKAN_SDK" "$SHELL_CONFIG" 2>/dev/null; then
    cat >> "$SHELL_CONFIG" << EOL

# Vulkan SDK Configuration
export VULKAN_SDK="$VULKAN_SDK_FOUND"
export PATH="\$VULKAN_SDK/bin:\$PATH"
export DYLD_LIBRARY_PATH="\$VULKAN_SDK/lib:\$DYLD_LIBRARY_PATH"
export VK_ICD_FILENAMES="\$VULKAN_SDK/share/vulkan/icd.d/MoltenVK_icd.json"
export VK_LAYER_PATH="\$VULKAN_SDK/share/vulkan/explicit_layer.d"
EOL
    echo "✓ Переменные окружения добавлены"
else
    echo "✓ Переменные окружения уже настроены"
fi

# Проверка компонентов
echo ""
echo "=== Проверка компонентов ==="

# Экспорт для текущей сессии
export VULKAN_SDK="$VULKAN_SDK_FOUND"
export PATH="$VULKAN_SDK/bin:$PATH"

# glslc
if command -v glslc &> /dev/null; then
    echo "✓ glslc найден: $(which glslc)"
else
    echo "✗ glslc не найден"
fi

# vulkaninfo
if command -v vulkaninfo &> /dev/null; then
    echo "✓ vulkaninfo найден"
    echo ""
    echo "Информация о Vulkan:"
    vulkaninfo --summary 2>/dev/null | head -n 20
else
    echo "✗ vulkaninfo не найден"
fi

echo ""
echo "=== Установка завершена ==="
echo ""
echo "Для применения настроек выполните одну из команд:"
echo "  source setup-env.sh        # только для текущей сессии"
echo "  source $SHELL_CONFIG       # постоянно"
echo ""
echo "Или перезапустите терминал"