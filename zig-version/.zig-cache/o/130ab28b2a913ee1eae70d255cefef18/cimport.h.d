cimport.o: \
  /Users/<USER>/Apps/tmp/vulkan/zig-version/.zig-cache/o/130ab28b2a913ee1eae70d255cefef18/cimport.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vulkan/vulkan.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vulkan/vk_platform.h \
  /opt/homebrew/Cellar/zig/0.14.0_1/lib/zig/include/stddef.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/stddef.h \
  /opt/homebrew/Cellar/zig/0.14.0_1/lib/zig/include/__stddef_header_macro.h \
  /opt/homebrew/Cellar/zig/0.14.0_1/lib/zig/include/__stddef_ptrdiff_t.h \
  /opt/homebrew/Cellar/zig/0.14.0_1/lib/zig/include/__stddef_size_t.h \
  /opt/homebrew/Cellar/zig/0.14.0_1/lib/zig/include/__stddef_wchar_t.h \
  /opt/homebrew/Cellar/zig/0.14.0_1/lib/zig/include/__stddef_null.h \
  /opt/homebrew/Cellar/zig/0.14.0_1/lib/zig/include/__stddef_max_align_t.h \
  /opt/homebrew/Cellar/zig/0.14.0_1/lib/zig/include/__stddef_offsetof.h \
  /opt/homebrew/Cellar/zig/0.14.0_1/lib/zig/include/stdint.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/stdint.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int8_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int16_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int32_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_int64_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint8_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint16_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint32_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/_types/_uint64_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/sys/_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/sys/cdefs.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/sys/_symbol_aliasing.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/sys/_posix_availability.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/machine/_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/arm/_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/sys/_pthread/_pthread_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_intptr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/sys/_types/_uintptr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/_types/_intmax_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/_types/_uintmax_t.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vulkan/vulkan_core.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vk_video/vulkan_video_codec_h264std.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vk_video/vulkan_video_codecs_common.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vk_video/vulkan_video_codec_h264std_encode.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vk_video/vulkan_video_codec_h265std.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vk_video/vulkan_video_codec_h265std_encode.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vk_video/vulkan_video_codec_h264std_decode.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vk_video/vulkan_video_codec_h265std_decode.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vk_video/vulkan_video_codec_av1std.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vk_video/vulkan_video_codec_av1std_decode.h \
  /opt/homebrew/Cellar/vulkan-loader/1.4.315/../../../opt/vulkan-headers/include/vk_video/vulkan_video_codec_av1std_encode.h \
  /opt/homebrew/include/GLFW/glfw3.h \
  /opt/homebrew/Cellar/zig/0.14.0_1/lib/zig/include/__stddef_rsize_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks/OpenGL.framework/Headers/gl.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks/OpenGL.framework/Headers/gltypes.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks/OpenGL.framework/Headers/OpenGLAvailability.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/os/availability.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternal.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityVersions.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/AvailabilityInternalLegacy.h
