# Vulkan Triangle - Zig Version

Это версия Vulkan triangle приложения, написанная на Zig с прямым использованием Vulkan C API.

## Особенности Zig версии

### Преимущества:
- **Отличная C interop** - прямое использование Vulkan C API без wrapper'ов
- **Компайл-тайм безопасность** - многие ошибки ловятся на этапе компиляции
- **Явное управление памятью** - нет скрытых аллокаций
- **Простой синтаксис** - проще чем C++, но мощнее чем C
- **Встроенная система сборки** - не нужны внешние build системы
- **Zero-cost abstractions** - высокоуровневые конструкции без overhead

### Используемые технологии:
- Прямые Vulkan C bindings через `@cImport`
- GLFW для создания окон
- Zig build system
- ArrayList для динамических массивов
- Allocator для управления памятью

## Сборка и запуск

### Требования:
- Zig 0.11+
- Vulkan SDK
- GLFW
- На macOS: MoltenVK

### Команды:
```bash
# Сборка
zig build

# Запуск
zig build run

# Сборка в release режиме
zig build -Doptimize=ReleaseFast
```

## Архитектура

### Основные отличия от C++ версии:

1. **Структуры с методами:**
```zig
const VulkanApp = struct {
    allocator: Allocator,
    instance: c.VkInstance,
    device: c.VkDevice,
    // ...

    pub fn init(allocator: Allocator) !Self {
        // ...
    }
};
```

2. **Явные аллокаторы:**
```zig
var app = try VulkanApp.init(allocator);
defer app.deinit();
```

3. **Error unions для обработки ошибок:**
```zig
fn createInstance(self: *Self) !void {
    const result = c.vkCreateInstance(&create_info, null, &self.instance);
    if (result != c.VK_SUCCESS) {
        return error.InstanceCreationFailed;
    }
}
```

4. **Компайл-тайм условная компиляция:**
```zig
const enable_validation_layers = std.debug.runtime_safety;
```

## C Interop

Zig отлично работает с C API:

```zig
const c = @cImport({
    @cInclude("vulkan/vulkan.h");
    @cInclude("GLFW/glfw3.h");
});

// Прямое использование C функций
const result = c.vkCreateInstance(&create_info, null, &self.instance);
```

## Управление памятью

Zig требует явного управления памятью:

```zig
var extensions = try self.allocator.alloc([*:0]const u8, extension_count);
defer self.allocator.free(extensions);
```

## Синхронизация

Zig версия будет включать то же исправление синхронизации семафоров:
- Отдельные семафоры для каждого изображения swapchain
- Правильная индексация по номеру изображения

## Статус

⚠️ **Примечание:** Это демонстрационная версия с упрощенной реализацией.
Полная версия потребует реализации всех методов создания Vulkan объектов.

### Реализовано:
- Базовая структура приложения
- Создание instance с validation layers
- Создание surface
- Основной event loop
- Система сборки

### Требует реализации:
- Выбор физического устройства
- Создание logical device
- Создание swapchain
- Создание render pass и pipeline
- Создание command buffers
- Создание объектов синхронизации
- Debug messenger

## Сравнение с C++ и Rust

| Аспект | C++ | Rust | Zig |
|--------|-----|------|-----|
| C interop | Хорошая | Через FFI | Отличная |
| Безопасность памяти | Ручная | Автоматическая | Явная |
| Время компиляции | Быстрое | Медленное | Быстрое |
| Размер бинарника | Средний | Большой | Маленький |
| Сложность синтаксиса | Высокая | Средняя | Низкая |
| Управление ошибками | Исключения | Result<T,E> | Error unions |
| Метапрограммирование | Шаблоны | Макросы | Comptime |

## Философия Zig

Zig стремится к:
- **Простоте** - минимум скрытой магии
- **Производительности** - zero-cost abstractions
- **Безопасности** - но не за счет производительности
- **Прагматизму** - легкая интеграция с существующим C кодом
