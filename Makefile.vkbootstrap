# Makefile для Vulkan Triangle с vkBootstrap

# Компилятор и флаги
CXX = clang++
CXXFLAGS = -std=c++17 -O2 -Wall -Wextra

# Пути к библиотекам
VULKAN_SDK_PATH = /Users/<USER>/VulkanSDK/1.4.313.0/macOS
GLFW_PATH = /opt/homebrew

# Include пути
INCLUDES = -I$(VULKAN_SDK_PATH)/include \
           -I$(GLFW_PATH)/include \
           -Ivk-bootstrap/src

# Библиотеки для линковки
LDFLAGS = -L$(VULKAN_SDK_PATH)/lib -lvulkan \
          -L$(GLFW_PATH)/lib -lglfw \
          -framework Cocoa -framework IOKit -framework CoreVideo

# Исходные файлы
SOURCES = main_vkbootstrap.cpp vk-bootstrap/src/VkBootstrap.cpp
OBJECTS = $(SOURCES:.cpp=.o)

# Целевой исполняемый файл
TARGET = VulkanTriangleBootstrap

# Шейдеры
VERTEX_SHADER = shaders/shader.vert
FRAGMENT_SHADER = shaders/shader.frag
VERTEX_SPV = shaders/vert.spv
FRAGMENT_SPV = shaders/frag.spv

.PHONY: all clean shaders

all: shaders $(TARGET)

# Компиляция шейдеров
shaders: $(VERTEX_SPV) $(FRAGMENT_SPV)
	@echo "Компиляция шейдеров..."

$(VERTEX_SPV): $(VERTEX_SHADER)
	@echo "Компиляция vertex shader..."
	@if [ ! -f $(VERTEX_SPV) ]; then \
		glslc $(VERTEX_SHADER) -o $(VERTEX_SPV); \
	else \
		echo "Vertex shader уже скомпилирован"; \
	fi

$(FRAGMENT_SPV): $(FRAGMENT_SHADER)
	@echo "Компиляция fragment shader..."
	@if [ ! -f $(FRAGMENT_SPV) ]; then \
		glslc $(FRAGMENT_SHADER) -o $(FRAGMENT_SPV); \
	else \
		echo "Fragment shader уже скомпилирован"; \
	fi

# Компиляция объектных файлов
%.o: %.cpp
	@echo "Компиляция $<..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Линковка исполняемого файла
$(TARGET): $(OBJECTS)
	@echo "Линковка $(TARGET)..."
	$(CXX) $(OBJECTS) $(LDFLAGS) -o $(TARGET)
	@echo "Сборка завершена!"

# Очистка
clean:
	@echo "Очистка..."
	rm -f $(OBJECTS) $(TARGET)
	rm -f $(VERTEX_SPV) $(FRAGMENT_SPV)

# Запуск
run: $(TARGET)
	./$(TARGET)
