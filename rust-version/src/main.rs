use ash::vk;
use ash::{Device, Entry, Instance};
use raw_window_handle::{HasRawDisplayH<PERSON>le, HasRawWindowHandle};
use std::ffi::{CStr, CString};
use std::os::raw::c_char;
use winit::{
    event::{Event, WindowEvent},
    event_loop::{ControlFlow, EventLoop},
    window::{Window, WindowBuilder},
};

const WIDTH: u32 = 800;
const HEIGHT: u32 = 600;
const MAX_FRAMES_IN_FLIGHT: usize = 2;

const VALIDATION_LAYERS: &[&str] = &["VK_LAYER_KHRONOS_validation"];

#[cfg(debug_assertions)]
const ENABLE_VALIDATION_LAYERS: bool = true;
#[cfg(not(debug_assertions))]
const ENABLE_VALIDATION_LAYERS: bool = false;

struct VulkanApp {
    entry: Entry,
    instance: Instance,
    debug_utils_loader: ash::extensions::ext::DebugUtils,
    debug_messenger: vk::DebugUtilsMessengerEXT,
    surface: vk::SurfaceKHR,
    surface_loader: ash::extensions::khr::Surface,
    physical_device: vk::PhysicalDevice,
    device: Device,
    graphics_queue: vk::Queue,
    present_queue: vk::Queue,
    swapchain_loader: ash::extensions::khr::Swapchain,
    swapchain: vk::SwapchainKHR,
    swapchain_images: Vec<vk::Image>,
    swapchain_image_format: vk::Format,
    swapchain_extent: vk::Extent2D,
    swapchain_image_views: Vec<vk::ImageView>,
    render_pass: vk::RenderPass,
    pipeline_layout: vk::PipelineLayout,
    graphics_pipeline: vk::Pipeline,
    framebuffers: Vec<vk::Framebuffer>,
    command_pool: vk::CommandPool,
    command_buffers: Vec<vk::CommandBuffer>,
    image_available_semaphores: Vec<vk::Semaphore>,
    render_finished_semaphores: Vec<vk::Semaphore>,
    in_flight_fences: Vec<vk::Fence>,
    current_frame: usize,
}

impl VulkanApp {
    fn new(window: &Window) -> Result<Self, Box<dyn std::error::Error>> {
        let entry = Entry::linked();
        let instance = Self::create_instance(&entry)?;

        let (debug_utils_loader, debug_messenger) = Self::setup_debug_messenger(&entry, &instance)?;

        let surface = Self::create_surface(&entry, &instance, window)?;
        let surface_loader = ash::extensions::khr::Surface::new(&entry, &instance);

        let physical_device = Self::pick_physical_device(&instance, &surface_loader, surface)?;
        let (device, graphics_queue, present_queue) = Self::create_logical_device(&instance, physical_device, &surface_loader, surface)?;

        let swapchain_loader = ash::extensions::khr::Swapchain::new(&instance, &device);
        let (swapchain, swapchain_images, swapchain_image_format, swapchain_extent) =
            Self::create_swapchain(&device, &swapchain_loader, &surface_loader, surface, physical_device)?;

        let swapchain_image_views = Self::create_image_views(&device, &swapchain_images, swapchain_image_format)?;
        let render_pass = Self::create_render_pass(&device, swapchain_image_format)?;
        let (graphics_pipeline, pipeline_layout) = Self::create_graphics_pipeline(&device, render_pass, swapchain_extent)?;
        let framebuffers = Self::create_framebuffers(&device, &swapchain_image_views, render_pass, swapchain_extent)?;
        let command_pool = Self::create_command_pool(&device, &instance, physical_device, &surface_loader, surface)?;
        let command_buffers = Self::create_command_buffers(&device, command_pool)?;
        let (image_available_semaphores, render_finished_semaphores, in_flight_fences) =
            Self::create_sync_objects(&device, swapchain_images.len())?;

        Ok(VulkanApp {
            entry,
            instance,
            debug_utils_loader,
            debug_messenger,
            surface,
            surface_loader,
            physical_device,
            device,
            graphics_queue,
            present_queue,
            swapchain_loader,
            swapchain,
            swapchain_images,
            swapchain_image_format,
            swapchain_extent,
            swapchain_image_views,
            render_pass,
            pipeline_layout,
            graphics_pipeline,
            framebuffers,
            command_pool,
            command_buffers,
            image_available_semaphores,
            render_finished_semaphores,
            in_flight_fences,
            current_frame: 0,
        })
    }

    fn create_instance(entry: &Entry) -> Result<Instance, Box<dyn std::error::Error>> {
        if ENABLE_VALIDATION_LAYERS && !Self::check_validation_layer_support(entry) {
            return Err("Validation layers requested, but not available!".into());
        }

        let app_name = CString::new("Vulkan Triangle Rust")?;
        let engine_name = CString::new("No Engine")?;

        let app_info = vk::ApplicationInfo::builder()
            .application_name(&app_name)
            .application_version(vk::make_api_version(0, 1, 0, 0))
            .engine_name(&engine_name)
            .engine_version(vk::make_api_version(0, 1, 0, 0))
            .api_version(vk::API_VERSION_1_0);

        let mut extension_names = vec![
            ash::extensions::khr::Surface::name().as_ptr(),
        ];

        // Platform specific extensions
        #[cfg(target_os = "macos")]
        {
            extension_names.push(ash::extensions::mvk::MacOSSurface::name().as_ptr());
            extension_names.push(ash::extensions::khr::PortabilityEnumeration::name().as_ptr());
        }

        if ENABLE_VALIDATION_LAYERS {
            extension_names.push(ash::extensions::ext::DebugUtils::name().as_ptr());
        }

        let layer_names: Vec<CString> = if ENABLE_VALIDATION_LAYERS {
            VALIDATION_LAYERS
                .iter()
                .map(|&s| CString::new(s).unwrap())
                .collect()
        } else {
            vec![]
        };

        let layer_names_raw: Vec<*const c_char> = layer_names
            .iter()
            .map(|raw_name| raw_name.as_ptr())
            .collect();

        let mut create_info = vk::InstanceCreateInfo::builder()
            .application_info(&app_info)
            .enabled_extension_names(&extension_names);

        #[cfg(target_os = "macos")]
        {
            create_info = create_info.flags(vk::InstanceCreateFlags::ENUMERATE_PORTABILITY_KHR);
        }

        if ENABLE_VALIDATION_LAYERS {
            create_info = create_info.enabled_layer_names(&layer_names_raw);
        }

        let instance = unsafe { entry.create_instance(&create_info, None)? };
        Ok(instance)
    }

    fn check_validation_layer_support(entry: &Entry) -> bool {
        let layer_properties = entry.enumerate_instance_layer_properties().unwrap_or_default();

        for required_layer in VALIDATION_LAYERS {
            let mut found = false;
            for layer_property in &layer_properties {
                let layer_name = unsafe { CStr::from_ptr(layer_property.layer_name.as_ptr()) };
                if layer_name.to_str().unwrap_or("") == *required_layer {
                    found = true;
                    break;
                }
            }
            if !found {
                return false;
            }
        }
        true
    }

    fn setup_debug_messenger(
        entry: &Entry,
        instance: &Instance,
    ) -> Result<(ash::extensions::ext::DebugUtils, vk::DebugUtilsMessengerEXT), Box<dyn std::error::Error>> {
        if !ENABLE_VALIDATION_LAYERS {
            return Ok((ash::extensions::ext::DebugUtils::new(entry, instance), vk::DebugUtilsMessengerEXT::null()));
        }

        let debug_utils_loader = ash::extensions::ext::DebugUtils::new(entry, instance);

        let debug_create_info = vk::DebugUtilsMessengerCreateInfoEXT::builder()
            .message_severity(
                vk::DebugUtilsMessageSeverityFlagsEXT::ERROR
                    | vk::DebugUtilsMessageSeverityFlagsEXT::WARNING
                    | vk::DebugUtilsMessageSeverityFlagsEXT::INFO,
            )
            .message_type(
                vk::DebugUtilsMessageTypeFlagsEXT::GENERAL
                    | vk::DebugUtilsMessageTypeFlagsEXT::VALIDATION
                    | vk::DebugUtilsMessageTypeFlagsEXT::PERFORMANCE,
            )
            .pfn_user_callback(Some(vulkan_debug_callback));

        let debug_messenger = unsafe {
            debug_utils_loader.create_debug_utils_messenger(&debug_create_info, None)?
        };

        Ok((debug_utils_loader, debug_messenger))
    }

    fn create_surface(
        entry: &Entry,
        instance: &Instance,
        window: &Window,
    ) -> Result<vk::SurfaceKHR, Box<dyn std::error::Error>> {
        use raw_window_handle::{RawDisplayHandle, RawWindowHandle};

        let display_handle = window.raw_display_handle();
        let window_handle = window.raw_window_handle();

        let surface = unsafe {
            match (display_handle, window_handle) {
                #[cfg(target_os = "macos")]
                (RawDisplayHandle::AppKit(_), RawWindowHandle::AppKit(window)) => {
                    use ash::extensions::mvk::MacOSSurface;
                    let macos_surface_loader = MacOSSurface::new(entry, instance);
                    let create_info = vk::MacOSSurfaceCreateInfoMVK::builder()
                        .view(window.ns_view);
                    macos_surface_loader.create_mac_os_surface(&create_info, None)?
                }
                _ => return Err("Unsupported platform".into()),
            }
        };

        Ok(surface)
    }

    fn pick_physical_device(
        instance: &Instance,
        surface_loader: &ash::extensions::khr::Surface,
        surface: vk::SurfaceKHR,
    ) -> Result<vk::PhysicalDevice, Box<dyn std::error::Error>> {
        let physical_devices = unsafe { instance.enumerate_physical_devices()? };

        for device in physical_devices {
            if Self::is_device_suitable(instance, device, surface_loader, surface)? {
                return Ok(device);
            }
        }

        Err("Failed to find a suitable GPU!".into())
    }

    fn is_device_suitable(
        instance: &Instance,
        device: vk::PhysicalDevice,
        surface_loader: &ash::extensions::khr::Surface,
        surface: vk::SurfaceKHR,
    ) -> Result<bool, Box<dyn std::error::Error>> {
        let queue_families = Self::find_queue_families(instance, device, surface_loader, surface)?;
        let extensions_supported = Self::check_device_extension_support(instance, device)?;

        let swapchain_adequate = if extensions_supported {
            let swapchain_support = Self::query_swapchain_support(device, surface_loader, surface)?;
            !swapchain_support.formats.is_empty() && !swapchain_support.present_modes.is_empty()
        } else {
            false
        };

        Ok(queue_families.is_complete() && extensions_supported && swapchain_adequate)
    }

    fn find_queue_families(
        instance: &Instance,
        device: vk::PhysicalDevice,
        surface_loader: &ash::extensions::khr::Surface,
        surface: vk::SurfaceKHR,
    ) -> Result<QueueFamilyIndices, Box<dyn std::error::Error>> {
        let queue_families = unsafe { instance.get_physical_device_queue_family_properties(device) };

        let mut indices = QueueFamilyIndices::new();

        for (index, queue_family) in queue_families.iter().enumerate() {
            if queue_family.queue_flags.contains(vk::QueueFlags::GRAPHICS) {
                indices.graphics_family = Some(index as u32);
            }

            let present_support = unsafe {
                surface_loader.get_physical_device_surface_support(device, index as u32, surface)?
            };

            if present_support {
                indices.present_family = Some(index as u32);
            }

            if indices.is_complete() {
                break;
            }
        }

        Ok(indices)
    }

    fn check_device_extension_support(
        instance: &Instance,
        device: vk::PhysicalDevice,
    ) -> Result<bool, Box<dyn std::error::Error>> {
        let available_extensions = unsafe {
            instance.enumerate_device_extension_properties(device)?
        };

        let required_extensions = vec![ash::extensions::khr::Swapchain::name()];

        for required in &required_extensions {
            let mut found = false;
            for available in &available_extensions {
                let available_name = unsafe { CStr::from_ptr(available.extension_name.as_ptr()) };
                if available_name == *required {
                    found = true;
                    break;
                }
            }
            if !found {
                return Ok(false);
            }
        }

        Ok(true)
    }

    fn query_swapchain_support(
        device: vk::PhysicalDevice,
        surface_loader: &ash::extensions::khr::Surface,
        surface: vk::SurfaceKHR,
    ) -> Result<SwapchainSupportDetails, Box<dyn std::error::Error>> {
        let capabilities = unsafe {
            surface_loader.get_physical_device_surface_capabilities(device, surface)?
        };

        let formats = unsafe {
            surface_loader.get_physical_device_surface_formats(device, surface)?
        };

        let present_modes = unsafe {
            surface_loader.get_physical_device_surface_present_modes(device, surface)?
        };

        Ok(SwapchainSupportDetails {
            capabilities,
            formats,
            present_modes,
        })
    }

    fn draw_frame(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        unsafe {
            self.device.wait_for_fences(
                &[self.in_flight_fences[self.current_frame]],
                true,
                u64::MAX,
            )?;

            let (image_index, _) = self.swapchain_loader.acquire_next_image(
                self.swapchain,
                u64::MAX,
                self.image_available_semaphores[self.current_frame % self.swapchain_images.len()],
                vk::Fence::null(),
            )?;

            self.device.reset_fences(&[self.in_flight_fences[self.current_frame]])?;

            self.device.reset_command_buffer(
                self.command_buffers[self.current_frame],
                vk::CommandBufferResetFlags::empty(),
            )?;

            self.record_command_buffer(self.command_buffers[self.current_frame], image_index)?;

            let wait_semaphores = [self.image_available_semaphores[self.current_frame % self.swapchain_images.len()]];
            let wait_stages = [vk::PipelineStageFlags::COLOR_ATTACHMENT_OUTPUT];
            let signal_semaphores = [self.render_finished_semaphores[image_index as usize]];

            let submit_info = vk::SubmitInfo::builder()
                .wait_semaphores(&wait_semaphores)
                .wait_dst_stage_mask(&wait_stages)
                .command_buffers(&[self.command_buffers[self.current_frame]])
                .signal_semaphores(&signal_semaphores);

            self.device.queue_submit(
                self.graphics_queue,
                &[submit_info.build()],
                self.in_flight_fences[self.current_frame],
            )?;

            let swapchains = [self.swapchain];
            let image_indices = [image_index];

            let present_info = vk::PresentInfoKHR::builder()
                .wait_semaphores(&signal_semaphores)
                .swapchains(&swapchains)
                .image_indices(&image_indices);

            self.swapchain_loader.queue_present(self.present_queue, &present_info)?;

            self.current_frame = (self.current_frame + 1) % MAX_FRAMES_IN_FLIGHT;
        }

        Ok(())
    }

    fn record_command_buffer(&self, command_buffer: vk::CommandBuffer, image_index: u32) -> Result<(), Box<dyn std::error::Error>> {
        unsafe {
            let begin_info = vk::CommandBufferBeginInfo::builder();
            self.device.begin_command_buffer(command_buffer, &begin_info)?;

            let clear_values = [vk::ClearValue {
                color: vk::ClearColorValue {
                    float32: [0.0, 0.0, 0.0, 1.0],
                },
            }];

            let render_pass_info = vk::RenderPassBeginInfo::builder()
                .render_pass(self.render_pass)
                .framebuffer(self.framebuffers[image_index as usize])
                .render_area(vk::Rect2D {
                    offset: vk::Offset2D { x: 0, y: 0 },
                    extent: self.swapchain_extent,
                })
                .clear_values(&clear_values);

            self.device.cmd_begin_render_pass(
                command_buffer,
                &render_pass_info,
                vk::SubpassContents::INLINE,
            );

            self.device.cmd_bind_pipeline(
                command_buffer,
                vk::PipelineBindPoint::GRAPHICS,
                self.graphics_pipeline,
            );

            self.device.cmd_draw(command_buffer, 3, 1, 0, 0);

            self.device.cmd_end_render_pass(command_buffer);
            self.device.end_command_buffer(command_buffer)?;
        }

        Ok(())
    }
}

#[derive(Debug, Clone)]
struct QueueFamilyIndices {
    graphics_family: Option<u32>,
    present_family: Option<u32>,
}

impl QueueFamilyIndices {
    fn new() -> Self {
        QueueFamilyIndices {
            graphics_family: None,
            present_family: None,
        }
    }

    fn is_complete(&self) -> bool {
        self.graphics_family.is_some() && self.present_family.is_some()
    }
}

#[derive(Debug, Clone)]
struct SwapchainSupportDetails {
    capabilities: vk::SurfaceCapabilitiesKHR,
    formats: Vec<vk::SurfaceFormatKHR>,
    present_modes: Vec<vk::PresentModeKHR>,
}

unsafe extern "system" fn vulkan_debug_callback(
    message_severity: vk::DebugUtilsMessageSeverityFlagsEXT,
    message_type: vk::DebugUtilsMessageTypeFlagsEXT,
    p_callback_data: *const vk::DebugUtilsMessengerCallbackDataEXT,
    _user_data: *mut std::os::raw::c_void,
) -> vk::Bool32 {
    let callback_data = *p_callback_data;
    let message_id_number = callback_data.message_id_number;

    let message_id_name = if callback_data.p_message_id_name.is_null() {
        std::borrow::Cow::from("")
    } else {
        CStr::from_ptr(callback_data.p_message_id_name).to_string_lossy()
    };

    let message = if callback_data.p_message.is_null() {
        std::borrow::Cow::from("")
    } else {
        CStr::from_ptr(callback_data.p_message).to_string_lossy()
    };

    println!(
        "{:?}:\n{:?} [{} ({})] : {}\n",
        message_severity,
        message_type,
        message_id_name,
        &message_id_number.to_string(),
        message,
    );

    vk::FALSE
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let event_loop = EventLoop::new();
    let window = WindowBuilder::new()
        .with_title("Vulkan Triangle - Rust")
        .with_inner_size(winit::dpi::LogicalSize::new(WIDTH, HEIGHT))
        .with_resizable(false)
        .build(&event_loop)?;

    let mut app = VulkanApp::new(&window)?;

    event_loop.run(move |event, _, control_flow| {
        *control_flow = ControlFlow::Poll;

        match event {
            Event::WindowEvent {
                event: WindowEvent::CloseRequested,
                ..
            } => {
                *control_flow = ControlFlow::Exit;
            }
            Event::MainEventsCleared => {
                if let Err(e) = app.draw_frame() {
                    eprintln!("Error drawing frame: {}", e);
                    *control_flow = ControlFlow::Exit;
                }
            }
            _ => {}
        }
    });
}
