# Vulkan Triangle - Rust Version

Это версия Vulkan triangle приложения, написанная на Rust с использованием библиотеки `ash`.

## Особенности Rust версии

### Преимущества:
- **Безопасность памяти** - Rust предотвращает утечки памяти и segfaults на этапе компиляции
- **Строгая система типов** - помогает избежать ошибок с Vulkan handles
- **Result<T, E>** - элегантная обработка ошибок Vulkan API
- **Ownership** - автоматическое управление ресурсами
- **Zero-cost abstractions** - высокоуровневый код без потери производительности

### Используемые библиотеки:
- `ash` - официальные Vulkan bindings для Rust
- `winit` - кроссплатформенная библиотека для создания окон
- `raw-window-handle` - интеграция между winit и Vulkan

## Сборка и запуск

### Требования:
- Rust 1.70+
- Vulkan SDK
- На macOS: MoltenVK

### Команды:
```bash
# Сборка
cargo build --release

# Запуск
cargo run --release
```

## Архитектура

### Основные отличия от C++ версии:

1. **Структуры вместо классов:**
```rust
struct VulkanApp {
    entry: Entry,
    instance: Instance,
    device: Device,
    // ...
}
```

2. **impl блоки для методов:**
```rust
impl VulkanApp {
    fn new(window: &Window) -> Result<Self, Box<dyn std::error::Error>> {
        // ...
    }
}
```

3. **Result для обработки ошибок:**
```rust
fn create_instance(entry: &Entry) -> Result<Instance, Box<dyn std::error::Error>> {
    // Vulkan API calls that can fail
}
```

4. **Ownership и borrowing:**
```rust
fn draw_frame(&mut self) -> Result<(), Box<dyn std::error::Error>> {
    // self is borrowed mutably
}
```

## Синхронизация

Rust версия включает то же исправление синхронизации семафоров:
- Отдельные семафоры для каждого изображения swapchain
- Правильная индексация по номеру изображения, а не кадра

## Статус

⚠️ **Примечание:** Это демонстрационная версия с упрощенной реализацией.
Полная версия потребует реализации всех методов создания Vulkan объектов.

### Реализовано:
- Базовая структура приложения
- Создание instance с validation layers
- Debug callback
- Основной event loop
- Логика рендеринга

### Требует реализации:
- Создание logical device
- Создание swapchain
- Создание render pass и pipeline
- Создание command buffers
- Создание объектов синхронизации

## Сравнение с C++

| Аспект | C++ | Rust |
|--------|-----|------|
| Безопасность памяти | Ручная | Автоматическая |
| Обработка ошибок | Исключения/коды | Result<T, E> |
| Управление ресурсами | RAII | Ownership |
| Null pointer safety | Нет | Есть (Option<T>) |
| Время компиляции | Быстрое | Медленное |
| Размер бинарника | Меньше | Больше |
