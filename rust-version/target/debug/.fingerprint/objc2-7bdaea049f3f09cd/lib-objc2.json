{"rustc": 15497389221046826682, "features": "[\"alloc\", \"apple\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"apple\", \"catch-all\", \"default\", \"exception\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"malloc\", \"malloc_buf\", \"objc2-proc-macros\", \"relax-void-encoding\", \"std\", \"unstable-apple-new\", \"unstable-autoreleasesafe\", \"unstable-c-unwind\", \"unstable-compiler-rt\", \"unstable-docsrs\", \"unstable-static-class\", \"unstable-static-class-inlined\", \"unstable-static-sel\", \"unstable-static-sel-inlined\", \"verify\"]", "target": 4904471945119229693, "profile": 8276155916380437441, "path": 14561620328056454174, "deps": [[6452682797676277333, "objc_sys", false, 12488258510395207346], [16905956580596851835, "objc2_encode", false, 5079189332021490329]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-7bdaea049f3f09cd/dep-lib-objc2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}