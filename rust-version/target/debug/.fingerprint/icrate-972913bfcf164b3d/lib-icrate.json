{"rustc": 15497389221046826682, "features": "[\"Foundation\", \"Foundation_NSAppleEventDescriptor\", \"Foundation_NSArray\", \"Foundation_NSAttributedString\", \"Foundation_NSData\", \"Foundation_NSDictionary\", \"Foundation_NSEnumerator\", \"Foundation_NSError\", \"Foundation_NSHashTable\", \"Foundation_NSMapTable\", \"Foundation_NSMutableAttributedString\", \"Foundation_NSNumber\", \"Foundation_NSProcessInfo\", \"Foundation_NSProgress\", \"Foundation_NSString\", \"Foundation_NSThread\", \"Foundation_NSValue\", \"alloc\", \"apple\", \"block\", \"block2\", \"default\", \"dispatch\", \"objc2\", \"objective-c\", \"std\"]", "declared_features": "[\"Accessibility\", \"Accessibility_AXBrailleMap\", \"Accessibility_AXCategoricalDataAxisDescriptor\", \"Accessibility_AXChartDescriptor\", \"Accessibility_AXCustomContent\", \"Accessibility_AXDataPoint\", \"Accessibility_AXDataPointValue\", \"Accessibility_AXDataSeriesDescriptor\", \"Accessibility_AXLiveAudioGraph\", \"Accessibility_AXNumericDataAxisDescriptor\", \"Accessibility_all\", \"AdServices\", \"AdServices_AAAttribution\", \"AdServices_all\", \"AdSupport\", \"AdSupport_ASIdentifierManager\", \"AdSupport_all\", \"AppKit\", \"AppKit_NSATSTypesetter\", \"AppKit_NSAccessibilityCustomAction\", \"AppKit_NSAccessibilityCustomRotor\", \"AppKit_NSAccessibilityCustomRotorItemResult\", \"AppKit_NSAccessibilityCustomRotorSearchParameters\", \"AppKit_NSAccessibilityElement\", \"AppKit_NSActionCell\", \"AppKit_NSAlert\", \"AppKit_NSAlignmentFeedbackFilter\", \"AppKit_NSAnimation\", \"AppKit_NSAnimationContext\", \"AppKit_NSAppearance\", \"AppKit_NSApplication\", \"AppKit_NSArrayController\", \"AppKit_NSBezierPath\", \"AppKit_NSBindingSelectionMarker\", \"AppKit_NSBitmapImageRep\", \"AppKit_NSBox\", \"AppKit_NSBrowser\", \"AppKit_NSBrowserCell\", \"AppKit_NSButton\", \"AppKit_NSButtonCell\", \"AppKit_NSButtonTouchBarItem\", \"AppKit_NSCachedImageRep\", \"AppKit_NSCandidateListTouchBarItem\", \"AppKit_NSCell\", \"AppKit_NSClickGestureRecognizer\", \"AppKit_NSClipView\", \"AppKit_NSCollectionLayoutAnchor\", \"AppKit_NSCollectionLayoutBoundarySupplementaryItem\", \"AppKit_NSCollectionLayoutDecorationItem\", \"AppKit_NSCollectionLayoutDimension\", \"AppKit_NSCollectionLayoutEdgeSpacing\", \"AppKit_NSCollectionLayoutGroup\", \"AppKit_NSCollectionLayoutGroupCustomItem\", \"AppKit_NSCollectionLayoutItem\", \"AppKit_NSCollectionLayoutSection\", \"AppKit_NSCollectionLayoutSize\", \"AppKit_NSCollectionLayoutSpacing\", \"AppKit_NSCollectionLayoutSupplementaryItem\", \"AppKit_NSCollectionView\", \"AppKit_NSCollectionViewCompositionalLayout\", \"AppKit_NSCollectionViewCompositionalLayoutConfiguration\", \"AppKit_NSCollectionViewDiffableDataSource\", \"AppKit_NSCollectionViewFlowLayout\", \"AppKit_NSCollectionViewFlowLayoutInvalidationContext\", \"AppKit_NSCollectionViewGridLayout\", \"AppKit_NSCollectionViewItem\", \"AppKit_NSCollectionViewLayout\", \"AppKit_NSCollectionViewLayoutAttributes\", \"AppKit_NSCollectionViewLayoutInvalidationContext\", \"AppKit_NSCollectionViewTransitionLayout\", \"AppKit_NSCollectionViewUpdateItem\", \"AppKit_NSColor\", \"AppKit_NSColorList\", \"AppKit_NSColorPanel\", \"AppKit_NSColorPicker\", \"AppKit_NSColorPickerTouchBarItem\", \"AppKit_NSColorSampler\", \"AppKit_NSColorSpace\", \"AppKit_NSColorWell\", \"AppKit_NSComboBox\", \"AppKit_NSComboBoxCell\", \"AppKit_NSComboButton\", \"AppKit_NSControl\", \"AppKit_NSController\", \"AppKit_NSCursor\", \"AppKit_NSCustomImageRep\", \"AppKit_NSCustomTouchBarItem\", \"AppKit_NSDataAsset\", \"AppKit_NSDatePicker\", \"AppKit_NSDatePickerCell\", \"AppKit_NSDictionaryController\", \"AppKit_NSDictionaryControllerKeyValuePair\", \"AppKit_NSDiffableDataSourceSnapshot\", \"AppKit_NSDockTile\", \"AppKit_NSDocument\", \"AppKit_NSDocumentController\", \"AppKit_NSDraggingImageComponent\", \"AppKit_NSDraggingItem\", \"AppKit_NSDraggingSession\", \"AppKit_NSDrawer\", \"AppKit_NSEPSImageRep\", \"AppKit_NSEvent\", \"AppKit_NSFilePromiseProvider\", \"AppKit_NSFilePromiseReceiver\", \"AppKit_NSFont\", \"AppKit_NSFontAssetRequest\", \"AppKit_NSFontCollection\", \"AppKit_NSFontDescriptor\", \"AppKit_NSFontManager\", \"AppKit_NSFontPanel\", \"AppKit_NSForm\", \"AppKit_NSFormCell\", \"AppKit_NSGestureRecognizer\", \"AppKit_NSGlyphGenerator\", \"AppKit_NSGlyphInfo\", \"AppKit_NSGradient\", \"AppKit_NSGraphicsContext\", \"AppKit_NSGridCell\", \"AppKit_NSGridColumn\", \"AppKit_NSGridRow\", \"AppKit_NSGridView\", \"AppKit_NSGroupTouchBarItem\", \"AppKit_NSHapticFeedbackManager\", \"AppKit_NSHelpManager\", \"AppKit_NSImage\", \"AppKit_NSImageCell\", \"AppKit_NSImageRep\", \"AppKit_NSImageSymbolConfiguration\", \"AppKit_NSImageView\", \"AppKit_NSInputManager\", \"AppKit_NSInputServer\", \"AppKit_NSLayoutAnchor\", \"AppKit_NSLayoutConstraint\", \"AppKit_NSLayoutDimension\", \"AppKit_NSLayoutGuide\", \"AppKit_NSLayoutManager\", \"AppKit_NSLayoutXAxisAnchor\", \"AppKit_NSLayoutYAxisAnchor\", \"AppKit_NSLevelIndicator\", \"AppKit_NSLevelIndicatorCell\", \"AppKit_NSMagnificationGestureRecognizer\", \"AppKit_NSMatrix\", \"AppKit_NSMediaLibraryBrowserController\", \"AppKit_NSMenu\", \"AppKit_NSMenuItem\", \"AppKit_NSMenuItemCell\", \"AppKit_NSMenuToolbarItem\", \"AppKit_NSMovie\", \"AppKit_NSMutableFontCollection\", \"AppKit_NSMutableParagraphStyle\", \"AppKit_NSNib\", \"AppKit_NSObjectController\", \"AppKit_NSOpenPanel\", \"AppKit_NSOutlineView\", \"AppKit_NSPDFImageRep\", \"AppKit_NSPDFInfo\", \"AppKit_NSPDFPanel\", \"AppKit_NSPICTImageRep\", \"AppKit_NSPageController\", \"AppKit_NSPageLayout\", \"AppKit_NSPanGestureRecognizer\", \"AppKit_NSPanel\", \"AppKit_NSParagraphStyle\", \"AppKit_NSPasteboard\", \"AppKit_NSPasteboardItem\", \"AppKit_NSPathCell\", \"AppKit_NSPathComponentCell\", \"AppKit_NSPathControl\", \"AppKit_NSPathControlItem\", \"AppKit_NSPersistentDocument\", \"AppKit_NSPickerTouchBarItem\", \"AppKit_NSPopUpButton\", \"AppKit_NSPopUpButtonCell\", \"AppKit_NSPopover\", \"AppKit_NSPopoverTouchBarItem\", \"AppKit_NSPredicateEditor\", \"AppKit_NSPredicateEditorRowTemplate\", \"AppKit_NSPressGestureRecognizer\", \"AppKit_NSPressureConfiguration\", \"AppKit_NSPreviewRepresentingActivityItem\", \"AppKit_NSPrintInfo\", \"AppKit_NSPrintOperation\", \"AppKit_NSPrintPanel\", \"AppKit_NSPrinter\", \"AppKit_NSProgressIndicator\", \"AppKit_NSResponder\", \"AppKit_NSRotationGestureRecognizer\", \"AppKit_NSRuleEditor\", \"AppKit_NSRulerMarker\", \"AppKit_NSRulerView\", \"AppKit_NSRunningApplication\", \"AppKit_NSSavePanel\", \"AppKit_NSScreen\", \"AppKit_NSScrollView\", \"AppKit_NSScroller\", \"AppKit_NSScrubber\", \"AppKit_NSScrubberArrangedView\", \"AppKit_NSScrubberFlowLayout\", \"AppKit_NSScrubberImageItemView\", \"AppKit_NSScrubberItemView\", \"AppKit_NSScrubberLayout\", \"AppKit_NSScrubberLayoutAttributes\", \"AppKit_NSScrubberProportionalLayout\", \"AppKit_NSScrubberSelectionStyle\", \"AppKit_NSScrubberSelectionView\", \"AppKit_NSScrubberTextItemView\", \"AppKit_NSSearchField\", \"AppKit_NSSearchFieldCell\", \"AppKit_NSSearchToolbarItem\", \"AppKit_NSSecureTextField\", \"AppKit_NSSecureTextFieldCell\", \"AppKit_NSSegmentedCell\", \"AppKit_NSSegmentedControl\", \"AppKit_NSShadow\", \"AppKit_NSSharingService\", \"AppKit_NSSharingServicePicker\", \"AppKit_NSSharingServicePickerToolbarItem\", \"AppKit_NSSharingServicePickerTouchBarItem\", \"AppKit_NSSlider\", \"AppKit_NSSliderAccessory\", \"AppKit_NSSliderAccessoryBehavior\", \"AppKit_NSSliderCell\", \"AppKit_NSSliderTouchBarItem\", \"AppKit_NSSound\", \"AppKit_NSSpeechRecognizer\", \"AppKit_NSSpeechSynthesizer\", \"AppKit_NSSpellChecker\", \"AppKit_NSSplitView\", \"AppKit_NSSplitViewController\", \"AppKit_NSSplitViewItem\", \"AppKit_NSStackView\", \"AppKit_NSStatusBar\", \"AppKit_NSStatusBarButton\", \"AppKit_NSStatusItem\", \"AppKit_NSStepper\", \"AppKit_NSStepperCell\", \"AppKit_NSStepperTouchBarItem\", \"AppKit_NSStoryboard\", \"AppKit_NSStoryboardSegue\", \"AppKit_NSStringDrawingContext\", \"AppKit_NSSwitch\", \"AppKit_NSTabView\", \"AppKit_NSTabViewController\", \"AppKit_NSTabViewItem\", \"AppKit_NSTableCellView\", \"AppKit_NSTableColumn\", \"AppKit_NSTableHeaderCell\", \"AppKit_NSTableHeaderView\", \"AppKit_NSTableRowView\", \"AppKit_NSTableView\", \"AppKit_NSTableViewDiffableDataSource\", \"AppKit_NSTableViewRowAction\", \"AppKit_NSText\", \"AppKit_NSTextAlternatives\", \"AppKit_NSTextAttachment\", \"AppKit_NSTextAttachmentCell\", \"AppKit_NSTextAttachmentViewProvider\", \"AppKit_NSTextBlock\", \"AppKit_NSTextCheckingController\", \"AppKit_NSTextContainer\", \"AppKit_NSTextContentManager\", \"AppKit_NSTextContentStorage\", \"AppKit_NSTextElement\", \"AppKit_NSTextField\", \"AppKit_NSTextFieldCell\", \"AppKit_NSTextFinder\", \"AppKit_NSTextInputContext\", \"AppKit_NSTextLayoutFragment\", \"AppKit_NSTextLayoutManager\", \"AppKit_NSTextLineFragment\", \"AppKit_NSTextList\", \"AppKit_NSTextListElement\", \"AppKit_NSTextParagraph\", \"AppKit_NSTextRange\", \"AppKit_NSTextSelection\", \"AppKit_NSTextSelectionNavigation\", \"AppKit_NSTextStorage\", \"AppKit_NSTextTab\", \"AppKit_NSTextTable\", \"AppKit_NSTextTableBlock\", \"AppKit_NSTextView\", \"AppKit_NSTextViewportLayoutController\", \"AppKit_NSTintConfiguration\", \"AppKit_NSTitlebarAccessoryViewController\", \"AppKit_NSTokenField\", \"AppKit_NSTokenFieldCell\", \"AppKit_NSToolbar\", \"AppKit_NSToolbarItem\", \"AppKit_NSToolbarItemGroup\", \"AppKit_NSTouch\", \"AppKit_NSTouchBar\", \"AppKit_NSTouchBarItem\", \"AppKit_NSTrackingArea\", \"AppKit_NSTrackingSeparatorToolbarItem\", \"AppKit_NSTreeController\", \"AppKit_NSTreeNode\", \"AppKit_NSTypesetter\", \"AppKit_NSUserDefaultsController\", \"AppKit_NSUserInterfaceCompressionOptions\", \"AppKit_NSView\", \"AppKit_NSViewAnimation\", \"AppKit_NSViewController\", \"AppKit_NSVisualEffectView\", \"AppKit_NSWindow\", \"AppKit_NSWindowController\", \"AppKit_NSWindowTab\", \"AppKit_NSWindowTabGroup\", \"AppKit_NSWorkspace\", \"AppKit_NSWorkspaceAuthorization\", \"AppKit_NSWorkspaceOpenConfiguration\", \"AppKit_all\", \"AuthenticationServices\", \"AuthenticationServices_ASAccountAuthenticationModificationController\", \"AuthenticationServices_ASAccountAuthenticationModificationExtensionContext\", \"AuthenticationServices_ASAccountAuthenticationModificationReplacePasswordWithSignInWithAppleRequest\", \"AuthenticationServices_ASAccountAuthenticationModificationRequest\", \"AuthenticationServices_ASAccountAuthenticationModificationUpgradePasswordToStrongPasswordRequest\", \"AuthenticationServices_ASAccountAuthenticationModificationViewController\", \"AuthenticationServices_ASAuthorization\", \"AuthenticationServices_ASAuthorizationAppleIDButton\", \"AuthenticationServices_ASAuthorizationAppleIDCredential\", \"AuthenticationServices_ASAuthorizationAppleIDProvider\", \"AuthenticationServices_ASAuthorizationAppleIDRequest\", \"AuthenticationServices_ASAuthorizationController\", \"AuthenticationServices_ASAuthorizationOpenIDRequest\", \"AuthenticationServices_ASAuthorizationPasswordProvider\", \"AuthenticationServices_ASAuthorizationPasswordRequest\", \"AuthenticationServices_ASAuthorizationPlatformPublicKeyCredentialAssertion\", \"AuthenticationServices_ASAuthorizationPlatformPublicKeyCredentialAssertionRequest\", \"AuthenticationServices_ASAuthorizationPlatformPublicKeyCredentialDescriptor\", \"AuthenticationServices_ASAuthorizationPlatformPublicKeyCredentialProvider\", \"AuthenticationServices_ASAuthorizationPlatformPublicKeyCredentialRegistration\", \"AuthenticationServices_ASAuthorizationPlatformPublicKeyCredentialRegistrationRequest\", \"AuthenticationServices_ASAuthorizationProviderExtensionAuthorizationRequest\", \"AuthenticationServices_ASAuthorizationProviderExtensionAuthorizationResult\", \"AuthenticationServices_ASAuthorizationProviderExtensionKerberosMapping\", \"AuthenticationServices_ASAuthorizationProviderExtensionLoginConfiguration\", \"AuthenticationServices_ASAuthorizationProviderExtensionLoginManager\", \"AuthenticationServices_ASAuthorizationPublicKeyCredentialParameters\", \"AuthenticationServices_ASAuthorizationRequest\", \"AuthenticationServices_ASAuthorizationSecurityKeyPublicKeyCredentialAssertion\", \"AuthenticationServices_ASAuthorizationSecurityKeyPublicKeyCredentialAssertionRequest\", \"AuthenticationServices_ASAuthorizationSecurityKeyPublicKeyCredentialDescriptor\", \"AuthenticationServices_ASAuthorizationSecurityKeyPublicKeyCredentialProvider\", \"AuthenticationServices_ASAuthorizationSecurityKeyPublicKeyCredentialRegistration\", \"AuthenticationServices_ASAuthorizationSecurityKeyPublicKeyCredentialRegistrationRequest\", \"AuthenticationServices_ASAuthorizationSingleSignOnCredential\", \"AuthenticationServices_ASAuthorizationSingleSignOnProvider\", \"AuthenticationServices_ASAuthorizationSingleSignOnRequest\", \"AuthenticationServices_ASCredentialIdentityStore\", \"AuthenticationServices_ASCredentialIdentityStoreState\", \"AuthenticationServices_ASCredentialProviderExtensionContext\", \"AuthenticationServices_ASCredentialProviderViewController\", \"AuthenticationServices_ASCredentialServiceIdentifier\", \"AuthenticationServices_ASPasswordCredential\", \"AuthenticationServices_ASPasswordCredentialIdentity\", \"AuthenticationServices_ASWebAuthenticationSession\", \"AuthenticationServices_ASWebAuthenticationSessionRequest\", \"AuthenticationServices_ASWebAuthenticationSessionWebBrowserSessionManager\", \"AuthenticationServices_all\", \"AutomaticAssessmentConfiguration\", \"AutomaticAssessmentConfiguration_AEAssessmentApplication\", \"AutomaticAssessmentConfiguration_AEAssessmentConfiguration\", \"AutomaticAssessmentConfiguration_AEAssessmentParticipantConfiguration\", \"AutomaticAssessmentConfiguration_AEAssessmentSession\", \"AutomaticAssessmentConfiguration_all\", \"Automator\", \"Automator_AMAction\", \"Automator_AMAppleScriptAction\", \"Automator_AMBundleAction\", \"Automator_AMShellScriptAction\", \"Automator_AMWorkflow\", \"Automator_AMWorkflowController\", \"Automator_AMWorkflowView\", \"Automator_AMWorkspace\", \"Automator_all\", \"BackgroundAssets\", \"BackgroundAssets_BAAppExtensionInfo\", \"BackgroundAssets_BADownload\", \"BackgroundAssets_BADownloadManager\", \"BackgroundAssets_BAURLDownload\", \"BackgroundAssets_all\", \"BackgroundTasks\", \"BackgroundTasks_BGAppRefreshTask\", \"BackgroundTasks_BGAppRefreshTaskRequest\", \"BackgroundTasks_BGProcessingTask\", \"BackgroundTasks_BGProcessingTaskRequest\", \"BackgroundTasks_BGTask\", \"BackgroundTasks_BGTaskRequest\", \"BackgroundTasks_BGTaskScheduler\", \"BackgroundTasks_all\", \"BusinessChat\", \"BusinessChat_BCChatAction\", \"BusinessChat_BCChatButton\", \"BusinessChat_all\", \"CallKit\", \"CallKit_CXAction\", \"CallKit_CXAnswerCallAction\", \"CallKit_CXCall\", \"CallKit_CXCallAction\", \"CallKit_CXCallController\", \"CallKit_CXCallDirectoryExtensionContext\", \"CallKit_CXCallDirectoryManager\", \"CallKit_CXCallDirectoryProvider\", \"CallKit_CXCallObserver\", \"CallKit_CXCallUpdate\", \"CallKit_CXEndCallAction\", \"CallKit_CXHandle\", \"CallKit_CXPlayDTMFCallAction\", \"CallKit_CXProvider\", \"CallKit_CXProviderConfiguration\", \"CallKit_CXSetGroupCallAction\", \"CallKit_CXSetHeldCallAction\", \"CallKit_CXSetMutedCallAction\", \"CallKit_CXStartCallAction\", \"CallKit_CXTransaction\", \"CallKit_all\", \"ClassKit\", \"ClassKit_CLSActivity\", \"ClassKit_CLSActivityItem\", \"ClassKit_CLSBinaryItem\", \"ClassKit_CLSContext\", \"ClassKit_CLSDataStore\", \"ClassKit_CLSObject\", \"ClassKit_CLSProgressReportingCapability\", \"ClassKit_CLSQuantityItem\", \"ClassKit_CLSScoreItem\", \"ClassKit_all\", \"CloudKit\", \"CloudKit_CKAcceptSharesOperation\", \"CloudKit_CKAllowedSharingOptions\", \"CloudKit_CKAsset\", \"CloudKit_CKContainer\", \"CloudKit_CKDatabase\", \"CloudKit_CKDatabaseNotification\", \"CloudKit_CKDatabaseOperation\", \"CloudKit_CKDatabaseSubscription\", \"CloudKit_CKDiscoverAllUserIdentitiesOperation\", \"CloudKit_CKDiscoverUserIdentitiesOperation\", \"CloudKit_CKFetchDatabaseChangesOperation\", \"CloudKit_CKFetchNotificationChangesOperation\", \"CloudKit_CKFetchRecordChangesOperation\", \"CloudKit_CKFetchRecordZoneChangesConfiguration\", \"CloudKit_CKFetchRecordZoneChangesOperation\", \"CloudKit_CKFetchRecordZoneChangesOptions\", \"CloudKit_CKFetchRecordZonesOperation\", \"CloudKit_CKFetchRecordsOperation\", \"CloudKit_CKFetchShareMetadataOperation\", \"CloudKit_CKFetchShareParticipantsOperation\", \"CloudKit_CKFetchSubscriptionsOperation\", \"CloudKit_CKFetchWebAuthTokenOperation\", \"CloudKit_CKLocationSortDescriptor\", \"CloudKit_CKMarkNotificationsReadOperation\", \"CloudKit_CKModifyBadgeOperation\", \"CloudKit_CKModifyRecordZonesOperation\", \"CloudKit_CKModifyRecordsOperation\", \"CloudKit_CKModifySubscriptionsOperation\", \"CloudKit_CKNotification\", \"CloudKit_CKNotificationID\", \"CloudKit_CKNotificationInfo\", \"CloudKit_CKOperation\", \"CloudKit_CKOperationConfiguration\", \"CloudKit_CKOperationGroup\", \"CloudKit_CKQuery\", \"CloudKit_CKQueryCursor\", \"CloudKit_CKQueryNotification\", \"CloudKit_CKQueryOperation\", \"CloudKit_CKQuerySubscription\", \"CloudKit_CKRecord\", \"CloudKit_CKRecordID\", \"CloudKit_CKRecordZone\", \"CloudKit_CKRecordZoneID\", \"CloudKit_CKRecordZoneNotification\", \"CloudKit_CKRecordZoneSubscription\", \"CloudKit_CKReference\", \"CloudKit_CKServerChangeToken\", \"CloudKit_CKShare\", \"CloudKit_CKShareMetadata\", \"CloudKit_CKShareParticipant\", \"CloudKit_CKSubscription\", \"CloudKit_CKSystemSharingUIObserver\", \"CloudKit_CKUserIdentity\", \"CloudKit_CKUserIdentityLookupInfo\", \"CloudKit_all\", \"Contacts\", \"Contacts_CNChangeHistoryAddContactEvent\", \"Contacts_CNChangeHistoryAddGroupEvent\", \"Contacts_CNChangeHistoryAddMemberToGroupEvent\", \"Contacts_CNChangeHistoryAddSubgroupToGroupEvent\", \"Contacts_CNChangeHistoryDeleteContactEvent\", \"Contacts_CNChangeHistoryDeleteGroupEvent\", \"Contacts_CNChangeHistoryDropEverythingEvent\", \"Contacts_CNChangeHistoryEvent\", \"Contacts_CNChangeHistoryFetchRequest\", \"Contacts_CNChangeHistoryRemoveMemberFromGroupEvent\", \"Contacts_CNChangeHistoryRemoveSubgroupFromGroupEvent\", \"Contacts_CNChangeHistoryUpdateContactEvent\", \"Contacts_CNChangeHistoryUpdateGroupEvent\", \"Contacts_CNContact\", \"Contacts_CNContactFetchRequest\", \"Contacts_CNContactFormatter\", \"Contacts_CNContactProperty\", \"Contacts_CNContactRelation\", \"Contacts_CNContactStore\", \"Contacts_CNContactVCardSerialization\", \"Contacts_CNContactsUserDefaults\", \"Contacts_CNContainer\", \"Contacts_CNFetchRequest\", \"Contacts_CNFetchResult\", \"Contacts_CNGroup\", \"Contacts_CNInstantMessageAddress\", \"Contacts_CNLabeledValue\", \"Contacts_CNMutableContact\", \"Contacts_CNMutableGroup\", \"Contacts_CNMutablePostalAddress\", \"Contacts_CNPhoneNumber\", \"Contacts_CNPostalAddress\", \"Contacts_CNPostalAddressFormatter\", \"Contacts_CNSaveRequest\", \"Contacts_CNSocialProfile\", \"Contacts_all\", \"CoreAnimation\", \"CoreAnimation_CAAnimation\", \"CoreAnimation_CAAnimationGroup\", \"CoreAnimation_CABasicAnimation\", \"CoreAnimation_CAConstraint\", \"CoreAnimation_CAConstraintLayoutManager\", \"CoreAnimation_CADisplayLink\", \"CoreAnimation_CAEDRMetadata\", \"CoreAnimation_CAEmitterCell\", \"CoreAnimation_CAEmitterLayer\", \"CoreAnimation_CAGradientLayer\", \"CoreAnimation_CAKeyframeAnimation\", \"CoreAnimation_CALayer\", \"CoreAnimation_CAMediaTimingFunction\", \"CoreAnimation_CAPropertyAnimation\", \"CoreAnimation_CARemoteLayerClient\", \"CoreAnimation_CARemoteLayerServer\", \"CoreAnimation_CARenderer\", \"CoreAnimation_CAReplicatorLayer\", \"CoreAnimation_CAScrollLayer\", \"CoreAnimation_CAShapeLayer\", \"CoreAnimation_CASpringAnimation\", \"CoreAnimation_CATextLayer\", \"CoreAnimation_CATiledLayer\", \"CoreAnimation_CATransaction\", \"CoreAnimation_CATransformLayer\", \"CoreAnimation_CATransition\", \"CoreAnimation_CAValueFunction\", \"CoreAnimation_all\", \"CoreData\", \"CoreData_NSAsynchronousFetchRequest\", \"CoreData_NSAsynchronousFetchResult\", \"CoreData_NSAtomicStore\", \"CoreData_NSAtomicStoreCacheNode\", \"CoreData_NSAttributeDescription\", \"CoreData_NSBatchDeleteRequest\", \"CoreData_NSBatchDeleteResult\", \"CoreData_NSBatchInsertRequest\", \"CoreData_NSBatchInsertResult\", \"CoreData_NSBatchUpdateRequest\", \"CoreData_NSBatchUpdateResult\", \"CoreData_NSConstraintConflict\", \"CoreData_NSCoreDataCoreSpotlightDelegate\", \"CoreData_NSDerivedAttributeDescription\", \"CoreData_NSEntityDescription\", \"CoreData_NSEntityMapping\", \"CoreData_NSEntityMigrationPolicy\", \"CoreData_NSExpressionDescription\", \"CoreData_NSFetchIndexDescription\", \"CoreData_NSFetchIndexElementDescription\", \"CoreData_NSFetchRequest\", \"CoreData_NSFetchRequestExpression\", \"CoreData_NSFetchedPropertyDescription\", \"CoreData_NSFetchedResultsController\", \"CoreData_NSIncrementalStore\", \"CoreData_NSIncrementalStoreNode\", \"CoreData_NSManagedObject\", \"CoreData_NSManagedObjectContext\", \"CoreData_NSManagedObjectID\", \"CoreData_NSManagedObjectModel\", \"CoreData_NSMappingModel\", \"CoreData_NSMergeConflict\", \"CoreData_NSMergePolicy\", \"CoreData_NSMigrationManager\", \"CoreData_NSPersistentCloudKitContainer\", \"CoreData_NSPersistentCloudKitContainerEvent\", \"CoreData_NSPersistentCloudKitContainerEventRequest\", \"CoreData_NSPersistentCloudKitContainerEventResult\", \"CoreData_NSPersistentCloudKitContainerOptions\", \"CoreData_NSPersistentContainer\", \"CoreData_NSPersistentHistoryChange\", \"CoreData_NSPersistentHistoryChangeRequest\", \"CoreData_NSPersistentHistoryResult\", \"CoreData_NSPersistentHistoryToken\", \"CoreData_NSPersistentHistoryTransaction\", \"CoreData_NSPersistentStore\", \"CoreData_NSPersistentStoreAsynchronousResult\", \"CoreData_NSPersistentStoreCoordinator\", \"CoreData_NSPersistentStoreDescription\", \"CoreData_NSPersistentStoreRequest\", \"CoreData_NSPersistentStoreResult\", \"CoreData_NSPropertyDescription\", \"CoreData_NSPropertyMapping\", \"CoreData_NSQueryGenerationToken\", \"CoreData_NSRelationshipDescription\", \"CoreData_NSSaveChangesRequest\", \"CoreData_all\", \"CoreLocation\", \"CoreLocation_CLBeacon\", \"CoreLocation_CLBeaconIdentityConstraint\", \"CoreLocation_CLBeaconRegion\", \"CoreLocation_CLCircularRegion\", \"CoreLocation_CLFloor\", \"CoreLocation_CLGeocoder\", \"CoreLocation_CLHeading\", \"CoreLocation_CLLocation\", \"CoreLocation_CLLocationManager\", \"CoreLocation_CLLocationSourceInformation\", \"CoreLocation_CLPlacemark\", \"CoreLocation_CLRegion\", \"CoreLocation_CLVisit\", \"CoreLocation_all\", \"DataDetection\", \"DataDetection_DDMatch\", \"DataDetection_DDMatchCalendarEvent\", \"DataDetection_DDMatchEmailAddress\", \"DataDetection_DDMatchFlightNumber\", \"DataDetection_DDMatchLink\", \"DataDetection_DDMatchMoneyAmount\", \"DataDetection_DDMatchPhoneNumber\", \"DataDetection_DDMatchPostalAddress\", \"DataDetection_DDMatchShipmentTrackingNumber\", \"DataDetection_all\", \"DeviceCheck\", \"DeviceCheck_DCAppAttestService\", \"DeviceCheck_DCDevice\", \"DeviceCheck_all\", \"EventKit\", \"EventKit_EKAlarm\", \"EventKit_EKCalendar\", \"EventKit_EKCalendarItem\", \"EventKit_EKEvent\", \"EventKit_EKEventStore\", \"EventKit_EKObject\", \"EventKit_EKParticipant\", \"EventKit_EKRecurrenceDayOfWeek\", \"EventKit_EKRecurrenceEnd\", \"EventKit_EKRecurrenceRule\", \"EventKit_EKReminder\", \"EventKit_EKSource\", \"EventKit_EKStructuredLocation\", \"EventKit_EKVirtualConferenceDescriptor\", \"EventKit_EKVirtualConferenceProvider\", \"EventKit_EKVirtualConferenceRoomTypeDescriptor\", \"EventKit_EKVirtualConferenceURLDescriptor\", \"EventKit_all\", \"ExceptionHandling\", \"ExceptionHandling_NSExceptionHandler\", \"ExceptionHandling_all\", \"ExtensionKit\", \"ExtensionKit_EXAppExtensionBrowserViewController\", \"ExtensionKit_EXHostViewController\", \"ExtensionKit_all\", \"ExternalAccessory\", \"ExternalAccessory_EAAccessory\", \"ExternalAccessory_EAAccessoryManager\", \"ExternalAccessory_EASession\", \"ExternalAccessory_EAWiFiUnconfiguredAccessory\", \"ExternalAccessory_EAWiFiUnconfiguredAccessoryBrowser\", \"ExternalAccessory_all\", \"FileProvider\", \"FileProviderUI\", \"FileProviderUI_FPUIActionExtensionContext\", \"FileProviderUI_FPUIActionExtensionViewController\", \"FileProviderUI_all\", \"FileProvider_NSFileProviderDomain\", \"FileProvider_NSFileProviderDomainVersion\", \"FileProvider_NSFileProviderExtension\", \"FileProvider_NSFileProviderItemVersion\", \"FileProvider_NSFileProviderManager\", \"FileProvider_NSFileProviderRequest\", \"FileProvider_all\", \"Foundation\", \"Foundation_NSAffineTransform\", \"Foundation_NSAppleEventDescriptor\", \"Foundation_NSAppleEventManager\", \"Foundation_NSAppleScript\", \"Foundation_NSArchiver\", \"Foundation_NSArray\", \"Foundation_NSAssertionHandler\", \"Foundation_NSAttributedString\", \"Foundation_NSAttributedStringMarkdownParsingOptions\", \"Foundation_NSAttributedStringMarkdownSourcePosition\", \"Foundation_NSAutoreleasePool\", \"Foundation_NSBackgroundActivityScheduler\", \"Foundation_NSBlockOperation\", \"Foundation_NSBundle\", \"Foundation_NSBundleResourceRequest\", \"Foundation_NSByteCountFormatter\", \"Foundation_NSCache\", \"Foundation_NSCachedURLResponse\", \"Foundation_NSCalendar\", \"Foundation_NSCalendarDate\", \"Foundation_NSCharacterSet\", \"Foundation_NSClassDescription\", \"Foundation_NSCloneCommand\", \"Foundation_NSCloseCommand\", \"Foundation_NSCoder\", \"Foundation_NSComparisonPredicate\", \"Foundation_NSCompoundPredicate\", \"Foundation_NSCondition\", \"Foundation_NSConditionLock\", \"Foundation_NSConnection\", \"Foundation_NSConstantString\", \"Foundation_NSCountCommand\", \"Foundation_NSCountedSet\", \"Foundation_NSCreateCommand\", \"Foundation_NSData\", \"Foundation_NSDataDetector\", \"Foundation_NSDate\", \"Foundation_NSDateComponents\", \"Foundation_NSDateComponentsFormatter\", \"Foundation_NSDateFormatter\", \"Foundation_NSDateInterval\", \"Foundation_NSDateIntervalFormatter\", \"Foundation_NSDecimalNumber\", \"Foundation_NSDecimalNumberHandler\", \"Foundation_NSDeleteCommand\", \"Foundation_NSDictionary\", \"Foundation_NSDimension\", \"Foundation_NSDirectoryEnumerator\", \"Foundation_NSDistantObject\", \"Foundation_NSDistantObjectRequest\", \"Foundation_NSDistributedLock\", \"Foundation_NSDistributedNotificationCenter\", \"Foundation_NSEnergyFormatter\", \"Foundation_NSEnumerator\", \"Foundation_NSError\", \"Foundation_NSException\", \"Foundation_NSExistsCommand\", \"Foundation_NSExpression\", \"Foundation_NSExtensionContext\", \"Foundation_NSExtensionItem\", \"Foundation_NSFileAccessIntent\", \"Foundation_NSFileCoordinator\", \"Foundation_NSFileHandle\", \"Foundation_NSFileManager\", \"Foundation_NSFileProviderService\", \"Foundation_NSFileSecurity\", \"Foundation_NSFileVersion\", \"Foundation_NSFileWrapper\", \"Foundation_NSFormatter\", \"Foundation_NSGarbageCollector\", \"Foundation_NSGetCommand\", \"Foundation_NSHTTPCookie\", \"Foundation_NSHTTPCookieStorage\", \"Foundation_NSHTTPURLResponse\", \"Foundation_NSHashTable\", \"Foundation_NSHost\", \"Foundation_NSISO8601DateFormatter\", \"Foundation_NSIndexPath\", \"Foundation_NSIndexSet\", \"Foundation_NSIndexSpecifier\", \"Foundation_NSInflectionRule\", \"Foundation_NSInflectionRuleExplicit\", \"Foundation_NSInputStream\", \"Foundation_NSInvocation\", \"Foundation_NSInvocationOperation\", \"Foundation_NSItemProvider\", \"Foundation_NSJSONSerialization\", \"Foundation_NSKeyedArchiver\", \"Foundation_NSKeyedUnarchiver\", \"Foundation_NSLengthFormatter\", \"Foundation_NSLinguisticTagger\", \"Foundation_NSListFormatter\", \"Foundation_NSLocale\", \"Foundation_NSLock\", \"Foundation_NSLogicalTest\", \"Foundation_NSMachBootstrapServer\", \"Foundation_NSMachPort\", \"Foundation_NSMapTable\", \"Foundation_NSMassFormatter\", \"Foundation_NSMeasurement\", \"Foundation_NSMeasurementFormatter\", \"Foundation_NSMessagePort\", \"Foundation_NSMessagePortNameServer\", \"Foundation_NSMetadataItem\", \"Foundation_NSMetadataQuery\", \"Foundation_NSMetadataQueryAttributeValueTuple\", \"Foundation_NSMetadataQueryResultGroup\", \"Foundation_NSMethodSignature\", \"Foundation_NSMiddleSpecifier\", \"Foundation_NSMorphology\", \"Foundation_NSMorphologyCustomPronoun\", \"Foundation_NSMoveCommand\", \"Foundation_NSMutableArray\", \"Foundation_NSMutableAttributedString\", \"Foundation_NSMutableCharacterSet\", \"Foundation_NSMutableData\", \"Foundation_NSMutableDictionary\", \"Foundation_NSMutableIndexSet\", \"Foundation_NSMutableOrderedSet\", \"Foundation_NSMutableSet\", \"Foundation_NSMutableString\", \"Foundation_NSMutableURLRequest\", \"Foundation_NSNameSpecifier\", \"Foundation_NSNetService\", \"Foundation_NSNetServiceBrowser\", \"Foundation_NSNotification\", \"Foundation_NSNotificationCenter\", \"Foundation_NSNotificationQueue\", \"Foundation_NSNull\", \"Foundation_NSNumber\", \"Foundation_NSNumberFormatter\", \"Foundation_NSOperation\", \"Foundation_NSOperationQueue\", \"Foundation_NSOrderedCollectionChange\", \"Foundation_NSOrderedCollectionDifference\", \"Foundation_NSOrderedSet\", \"Foundation_NSOrthography\", \"Foundation_NSOutputStream\", \"Foundation_NSPersonNameComponents\", \"Foundation_NSPersonNameComponentsFormatter\", \"Foundation_NSPipe\", \"Foundation_NSPointerArray\", \"Foundation_NSPointerFunctions\", \"Foundation_NSPort\", \"Foundation_NSPortCoder\", \"Foundation_NSPortMessage\", \"Foundation_NSPortNameServer\", \"Foundation_NSPositionalSpecifier\", \"Foundation_NSPredicate\", \"Foundation_NSPresentationIntent\", \"Foundation_NSProcessInfo\", \"Foundation_NSProgress\", \"Foundation_NSPropertyListSerialization\", \"Foundation_NSPropertySpecifier\", \"Foundation_NSProtocolChecker\", \"Foundation_NSProxy\", \"Foundation_NSPurgeableData\", \"Foundation_NSQuitCommand\", \"Foundation_NSRandomSpecifier\", \"Foundation_NSRangeSpecifier\", \"Foundation_NSRecursiveLock\", \"Foundation_NSRegularExpression\", \"Foundation_NSRelativeDateTimeFormatter\", \"Foundation_NSRelativeSpecifier\", \"Foundation_NSRunLoop\", \"Foundation_NSScanner\", \"Foundation_NSScriptClassDescription\", \"Foundation_NSScriptCoercionHandler\", \"Foundation_NSScriptCommand\", \"Foundation_NSScriptCommandDescription\", \"Foundation_NSScriptExecutionContext\", \"Foundation_NSScriptObjectSpecifier\", \"Foundation_NSScriptSuiteRegistry\", \"Foundation_NSScriptWhoseTest\", \"Foundation_NSSecureUnarchiveFromDataTransformer\", \"Foundation_NSSet\", \"Foundation_NSSetCommand\", \"Foundation_NSSimpleCString\", \"Foundation_NSSocketPort\", \"Foundation_NSSocketPortNameServer\", \"Foundation_NSSortDescriptor\", \"Foundation_NSSpecifierTest\", \"Foundation_NSSpellServer\", \"Foundation_NSStream\", \"Foundation_NSString\", \"Foundation_NSTask\", \"Foundation_NSTextCheckingResult\", \"Foundation_NSThread\", \"Foundation_NSTimeZone\", \"Foundation_NSTimer\", \"Foundation_NSURL\", \"Foundation_NSURLAuthenticationChallenge\", \"Foundation_NSURLCache\", \"Foundation_NSURLComponents\", \"Foundation_NSURLConnection\", \"Foundation_NSURLCredential\", \"Foundation_NSURLCredentialStorage\", \"Foundation_NSURLDownload\", \"Foundation_NSURLHandle\", \"Foundation_NSURLProtectionSpace\", \"Foundation_NSURLProtocol\", \"Foundation_NSURLQueryItem\", \"Foundation_NSURLRequest\", \"Foundation_NSURLResponse\", \"Foundation_NSURLSession\", \"Foundation_NSURLSessionConfiguration\", \"Foundation_NSURLSessionDataTask\", \"Foundation_NSURLSessionDownloadTask\", \"Foundation_NSURLSessionStreamTask\", \"Foundation_NSURLSessionTask\", \"Foundation_NSURLSessionTaskMetrics\", \"Foundation_NSURLSessionTaskTransactionMetrics\", \"Foundation_NSURLSessionUploadTask\", \"Foundation_NSURLSessionWebSocketMessage\", \"Foundation_NSURLSessionWebSocketTask\", \"Foundation_NSUUID\", \"Foundation_NSUbiquitousKeyValueStore\", \"Foundation_NSUnarchiver\", \"Foundation_NSUndoManager\", \"Foundation_NSUniqueIDSpecifier\", \"Foundation_NSUnit\", \"Foundation_NSUnitAcceleration\", \"Foundation_NSUnitAngle\", \"Foundation_NSUnitArea\", \"Foundation_NSUnitConcentrationMass\", \"Foundation_NSUnitConverter\", \"Foundation_NSUnitConverterLinear\", \"Foundation_NSUnitDispersion\", \"Foundation_NSUnitDuration\", \"Foundation_NSUnitElectricCharge\", \"Foundation_NSUnitElectricCurrent\", \"Foundation_NSUnitElectricPotentialDifference\", \"Foundation_NSUnitElectricResistance\", \"Foundation_NSUnitEnergy\", \"Foundation_NSUnitFrequency\", \"Foundation_NSUnitFuelEfficiency\", \"Foundation_NSUnitIlluminance\", \"Foundation_NSUnitInformationStorage\", \"Foundation_NSUnitLength\", \"Foundation_NSUnitMass\", \"Foundation_NSUnitPower\", \"Foundation_NSUnitPressure\", \"Foundation_NSUnitSpeed\", \"Foundation_NSUnitTemperature\", \"Foundation_NSUnitVolume\", \"Foundation_NSUserActivity\", \"Foundation_NSUserAppleScriptTask\", \"Foundation_NSUserAutomatorTask\", \"Foundation_NSUserDefaults\", \"Foundation_NSUserNotification\", \"Foundation_NSUserNotificationAction\", \"Foundation_NSUserNotificationCenter\", \"Foundation_NSUserScriptTask\", \"Foundation_NSUserUnixTask\", \"Foundation_NSValue\", \"Foundation_NSValueTransformer\", \"Foundation_NSWhoseSpecifier\", \"Foundation_NSXMLDTD\", \"Foundation_NSXMLDTDNode\", \"Foundation_NSXMLDocument\", \"Foundation_NSXMLElement\", \"Foundation_NSXMLNode\", \"Foundation_NSXMLParser\", \"Foundation_NSXPCCoder\", \"Foundation_NSXPCConnection\", \"Foundation_NSXPCInterface\", \"Foundation_NSXPCListener\", \"Foundation_NSXPCListenerEndpoint\", \"Foundation_all\", \"GameController\", \"GameController_GCColor\", \"GameController_GCController\", \"GameController_GCControllerAxisInput\", \"GameController_GCControllerButtonInput\", \"GameController_GCControllerDirectionPad\", \"GameController_GCControllerElement\", \"GameController_GCControllerTouchpad\", \"GameController_GCDeviceBattery\", \"GameController_GCDeviceCursor\", \"GameController_GCDeviceHaptics\", \"GameController_GCDeviceLight\", \"GameController_GCDirectionalGamepad\", \"GameController_GCDualSenseAdaptiveTrigger\", \"GameController_GCDualSenseGamepad\", \"GameController_GCDualShockGamepad\", \"GameController_GCEventViewController\", \"GameController_GCExtendedGamepad\", \"GameController_GCExtendedGamepadSnapshot\", \"GameController_GCGamepad\", \"GameController_GCGamepadSnapshot\", \"GameController_GCGearShifterElement\", \"GameController_GCKeyboard\", \"GameController_GCKeyboardInput\", \"GameController_GCMicroGamepad\", \"GameController_GCMicroGamepadSnapshot\", \"GameController_GCMotion\", \"GameController_GCMouse\", \"GameController_GCMouseInput\", \"GameController_GCPhysicalInputElementCollection\", \"GameController_GCPhysicalInputProfile\", \"GameController_GCRacingWheel\", \"GameController_GCRacingWheelInput\", \"GameController_GCRacingWheelInputState\", \"GameController_GCSteeringWheelElement\", \"GameController_GCXboxGamepad\", \"GameController_all\", \"GameKit\", \"GameKit_GKAccessPoint\", \"GameKit_GKAchievement\", \"GameKit_GKAchievementChallenge\", \"GameKit_GKAchievementDescription\", \"GameKit_GKAchievementViewController\", \"GameKit_GKBasePlayer\", \"GameKit_GKChallenge\", \"GameKit_GKChallengeEventHandler\", \"GameKit_GKChallengesViewController\", \"GameKit_GKCloudPlayer\", \"GameKit_GKDialogController\", \"GameKit_GKFriendRequestComposeViewController\", \"GameKit_GKGameCenterViewController\", \"GameKit_GKGameSession\", \"GameKit_GKInvite\", \"GameKit_GKLeaderboard\", \"GameKit_GKLeaderboardEntry\", \"GameKit_GKLeaderboardScore\", \"GameKit_GKLeaderboardSet\", \"GameKit_GKLeaderboardViewController\", \"GameKit_GKLocalPlayer\", \"GameKit_GKMatch\", \"GameKit_GKMatchRequest\", \"GameKit_GKMatchmaker\", \"GameKit_GKMatchmakerViewController\", \"GameKit_GKNotificationBanner\", \"GameKit_GKPlayer\", \"GameKit_GKSavedGame\", \"GameKit_GKScore\", \"GameKit_GKScoreChallenge\", \"GameKit_GKSession\", \"GameKit_GKTurnBasedEventHandler\", \"GameKit_GKTurnBasedExchange\", \"GameKit_GKTurnBasedExchangeReply\", \"GameKit_GKTurnBasedMatch\", \"GameKit_GKTurnBasedMatchmakerViewController\", \"GameKit_GKTurnBasedParticipant\", \"GameKit_GKVoiceChat\", \"GameKit_GKVoiceChatService\", \"GameKit_all\", \"HealthKit\", \"HealthKit_HKActivityMoveModeObject\", \"HealthKit_HKActivitySummary\", \"HealthKit_HKActivitySummaryQuery\", \"HealthKit_HKActivitySummaryType\", \"HealthKit_HKAnchoredObjectQuery\", \"HealthKit_HKAttachment\", \"HealthKit_HKAttachmentStore\", \"HealthKit_HKAudiogramSample\", \"HealthKit_HKAudiogramSampleType\", \"HealthKit_HKAudiogramSensitivityPoint\", \"HealthKit_HKBiologicalSexObject\", \"HealthKit_HKBloodTypeObject\", \"HealthKit_HKCDADocument\", \"HealthKit_HKCDADocumentSample\", \"HealthKit_HKCategorySample\", \"HealthKit_HKCategoryType\", \"HealthKit_HKCharacteristicType\", \"HealthKit_HKClinicalRecord\", \"HealthKit_HKClinicalType\", \"HealthKit_HKContactsLensSpecification\", \"HealthKit_HKContactsPrescription\", \"HealthKit_HKCorrelation\", \"HealthKit_HKCorrelationQuery\", \"HealthKit_HKCorrelationType\", \"HealthKit_HKCumulativeQuantitySample\", \"HealthKit_HKCumulativeQuantitySeriesSample\", \"HealthKit_HKDeletedObject\", \"HealthKit_HKDevice\", \"HealthKit_HKDiscreteQuantitySample\", \"HealthKit_HKDocumentQuery\", \"HealthKit_HKDocumentSample\", \"HealthKit_HKDocumentType\", \"HealthKit_HKElectrocardiogram\", \"HealthKit_HKElectrocardiogramQuery\", \"HealthKit_HKElectrocardiogramType\", \"HealthKit_HKElectrocardiogramVoltageMeasurement\", \"HealthKit_HKFHIRResource\", \"HealthKit_HKFHIRVersion\", \"HealthKit_HKFitzpatrickSkinTypeObject\", \"HealthKit_HKGlassesLensSpecification\", \"HealthKit_HKGlassesPrescription\", \"HealthKit_HKHealthStore\", \"HealthKit_HKHeartbeatSeriesBuilder\", \"HealthKit_HKHeartbeatSeriesQuery\", \"HealthKit_HKHeartbeatSeriesSample\", \"HealthKit_HKLensSpecification\", \"HealthKit_HKLiveWorkoutBuilder\", \"HealthKit_HKLiveWorkoutDataSource\", \"HealthKit_HKObject\", \"HealthKit_HKObjectType\", \"HealthKit_HKObserverQuery\", \"HealthKit_HKPrescriptionType\", \"HealthKit_HKQuantity\", \"HealthKit_HKQuantitySample\", \"HealthKit_HKQuantitySeriesSampleBuilder\", \"HealthKit_HKQuantitySeriesSampleQuery\", \"HealthKit_HKQuantityType\", \"HealthKit_HKQuery\", \"HealthKit_HKQueryAnchor\", \"HealthKit_HKQueryDescriptor\", \"HealthKit_HKSample\", \"HealthKit_HKSampleQuery\", \"HealthKit_HKSampleType\", \"HealthKit_HKSeriesBuilder\", \"HealthKit_HKSeriesSample\", \"HealthKit_HKSeriesType\", \"HealthKit_HKSource\", \"HealthKit_HKSourceQuery\", \"HealthKit_HKSourceRevision\", \"HealthKit_HKStatistics\", \"HealthKit_HKStatisticsCollection\", \"HealthKit_HKStatisticsCollectionQuery\", \"HealthKit_HKStatisticsQuery\", \"HealthKit_HKUnit\", \"HealthKit_HKVerifiableClinicalRecord\", \"HealthKit_HKVerifiableClinicalRecordQuery\", \"HealthKit_HKVerifiableClinicalRecordSubject\", \"HealthKit_HKVisionPrescription\", \"HealthKit_HKVisionPrism\", \"HealthKit_HKWheelchairUseObject\", \"HealthKit_HKWorkout\", \"HealthKit_HKWorkoutActivity\", \"HealthKit_HKWorkoutBuilder\", \"HealthKit_HKWorkoutConfiguration\", \"HealthKit_HKWorkoutEvent\", \"HealthKit_HKWorkoutRoute\", \"HealthKit_HKWorkoutRouteBuilder\", \"HealthKit_HKWorkoutRouteQuery\", \"HealthKit_HKWorkoutSession\", \"HealthKit_HKWorkoutType\", \"HealthKit_all\", \"IdentityLookup\", \"IdentityLookup_ILCallClassificationRequest\", \"IdentityLookup_ILCallCommunication\", \"IdentityLookup_ILClassificationRequest\", \"IdentityLookup_ILClassificationResponse\", \"IdentityLookup_ILCommunication\", \"IdentityLookup_ILMessageClassificationRequest\", \"IdentityLookup_ILMessageCommunication\", \"IdentityLookup_ILMessageFilterCapabilitiesQueryRequest\", \"IdentityLookup_ILMessageFilterCapabilitiesQueryResponse\", \"IdentityLookup_ILMessageFilterExtension\", \"IdentityLookup_ILMessageFilterExtensionContext\", \"IdentityLookup_ILMessageFilterQueryRequest\", \"IdentityLookup_ILMessageFilterQueryResponse\", \"IdentityLookup_ILNetworkResponse\", \"IdentityLookup_all\", \"InputMethodKit\", \"InputMethodKit_IMKCandidates\", \"InputMethodKit_IMKInputController\", \"InputMethodKit_IMKServer\", \"InputMethodKit_all\", \"LinkPresentation\", \"LinkPresentation_LPLinkMetadata\", \"LinkPresentation_LPLinkView\", \"LinkPresentation_LPMetadataProvider\", \"LinkPresentation_all\", \"LocalAuthentication\", \"LocalAuthenticationEmbeddedUI\", \"LocalAuthenticationEmbeddedUI_LAAuthenticationView\", \"LocalAuthenticationEmbeddedUI_all\", \"LocalAuthentication_LAAuthenticationRequirement\", \"LocalAuthentication_LABiometryFallbackRequirement\", \"LocalAuthentication_LAContext\", \"LocalAuthentication_LAPersistedRight\", \"LocalAuthentication_LAPrivateKey\", \"LocalAuthentication_LAPublicKey\", \"LocalAuthentication_LARight\", \"LocalAuthentication_LARightStore\", \"LocalAuthentication_LASecret\", \"LocalAuthentication_all\", \"MailKit\", \"MailKit_MEAddressAnnotation\", \"MailKit_MEComposeContext\", \"MailKit_MEComposeSession\", \"MailKit_MEDecodedMessage\", \"MailKit_MEDecodedMessageBanner\", \"MailKit_MEEmailAddress\", \"MailKit_MEEncodedOutgoingMessage\", \"MailKit_MEExtensionManager\", \"MailKit_MEExtensionViewController\", \"MailKit_MEMessage\", \"MailKit_MEMessageAction\", \"MailKit_MEMessageActionDecision\", \"MailKit_MEMessageEncodingResult\", \"MailKit_MEMessageSecurityInformation\", \"MailKit_MEMessageSigner\", \"MailKit_MEOutgoingMessageEncodingStatus\", \"MailKit_all\", \"MapKit\", \"MapKit_MKAnnotationView\", \"MapKit_MKCircle\", \"MapKit_MKCircleRenderer\", \"MapKit_MKClusterAnnotation\", \"MapKit_MKCompassButton\", \"MapKit_MKDirections\", \"MapKit_MKDirectionsRequest\", \"MapKit_MKDirectionsResponse\", \"MapKit_MKDistanceFormatter\", \"MapKit_MKETAResponse\", \"MapKit_MKGeoJSONDecoder\", \"MapKit_MKGeoJSONFeature\", \"MapKit_MKGeodesicPolyline\", \"MapKit_MKGradientPolylineRenderer\", \"MapKit_MKHybridMapConfiguration\", \"MapKit_MKImageryMapConfiguration\", \"MapKit_MKLocalPointsOfInterestRequest\", \"MapKit_MKLocalSearch\", \"MapKit_MKLocalSearchCompleter\", \"MapKit_MKLocalSearchCompletion\", \"MapKit_MKLocalSearchRequest\", \"MapKit_MKLocalSearchResponse\", \"MapKit_MKLookAroundScene\", \"MapKit_MKLookAroundSceneRequest\", \"MapKit_MKLookAroundSnapshot\", \"MapKit_MKLookAroundSnapshotOptions\", \"MapKit_MKLookAroundSnapshotter\", \"MapKit_MKLookAroundViewController\", \"MapKit_MKMapCamera\", \"MapKit_MKMapCameraBoundary\", \"MapKit_MKMapCameraZoomRange\", \"MapKit_MKMapConfiguration\", \"MapKit_MKMapItem\", \"MapKit_MKMapSnapshot\", \"MapKit_MKMapSnapshotOptions\", \"MapKit_MKMapSnapshotter\", \"MapKit_MKMapView\", \"MapKit_MKMarkerAnnotationView\", \"MapKit_MKMultiPoint\", \"MapKit_MKMultiPolygon\", \"MapKit_MKMultiPolygonRenderer\", \"MapKit_MKMultiPolyline\", \"MapKit_MKMultiPolylineRenderer\", \"MapKit_MKOverlayPathRenderer\", \"MapKit_MKOverlayRenderer\", \"MapKit_MKPinAnnotationView\", \"MapKit_MKPitchControl\", \"MapKit_MKPlacemark\", \"MapKit_MKPointAnnotation\", \"MapKit_MKPointOfInterestFilter\", \"MapKit_MKPolygon\", \"MapKit_MKPolygonRenderer\", \"MapKit_MKPolyline\", \"MapKit_MKPolylineRenderer\", \"MapKit_MKRoute\", \"MapKit_MKRouteStep\", \"MapKit_MKShape\", \"MapKit_MKStandardMapConfiguration\", \"MapKit_MKTileOverlay\", \"MapKit_MKTileOverlayRenderer\", \"MapKit_MKUserLocation\", \"MapKit_MKUserLocationView\", \"MapKit_MKZoomControl\", \"MapKit_all\", \"MediaPlayer\", \"MediaPlayer_MPAdTimeRange\", \"MediaPlayer_MPChangeLanguageOptionCommandEvent\", \"MediaPlayer_MPChangePlaybackPositionCommand\", \"MediaPlayer_MPChangePlaybackPositionCommandEvent\", \"MediaPlayer_MPChangePlaybackRateCommand\", \"MediaPlayer_MPChangePlaybackRateCommandEvent\", \"MediaPlayer_MPChangeRepeatModeCommand\", \"MediaPlayer_MPChangeRepeatModeCommandEvent\", \"MediaPlayer_MPChangeShuffleModeCommand\", \"MediaPlayer_MPChangeShuffleModeCommandEvent\", \"MediaPlayer_MPContentItem\", \"MediaPlayer_MPFeedbackCommand\", \"MediaPlayer_MPFeedbackCommandEvent\", \"MediaPlayer_MPMediaEntity\", \"MediaPlayer_MPMediaItem\", \"MediaPlayer_MPMediaItemArtwork\", \"MediaPlayer_MPMediaItemCollection\", \"MediaPlayer_MPMediaLibrary\", \"MediaPlayer_MPMediaPlaylist\", \"MediaPlayer_MPMediaPlaylistCreationMetadata\", \"MediaPlayer_MPMediaPredicate\", \"MediaPlayer_MPMediaPropertyPredicate\", \"MediaPlayer_MPMediaQuery\", \"MediaPlayer_MPMediaQuerySection\", \"MediaPlayer_MPMusicPlayerApplicationController\", \"MediaPlayer_MPMusicPlayerController\", \"MediaPlayer_MPMusicPlayerControllerMutableQueue\", \"MediaPlayer_MPMusicPlayerControllerQueue\", \"MediaPlayer_MPMusicPlayerMediaItemQueueDescriptor\", \"MediaPlayer_MPMusicPlayerPlayParameters\", \"MediaPlayer_MPMusicPlayerPlayParametersQueueDescriptor\", \"MediaPlayer_MPMusicPlayerQueueDescriptor\", \"MediaPlayer_MPMusicPlayerStoreQueueDescriptor\", \"MediaPlayer_MPNowPlayingInfoCenter\", \"MediaPlayer_MPNowPlayingInfoLanguageOption\", \"MediaPlayer_MPNowPlayingInfoLanguageOptionGroup\", \"MediaPlayer_MPNowPlayingSession\", \"MediaPlayer_MPPlayableContentManager\", \"MediaPlayer_MPPlayableContentManagerContext\", \"MediaPlayer_MPRatingCommand\", \"MediaPlayer_MPRatingCommandEvent\", \"MediaPlayer_MPRemoteCommand\", \"MediaPlayer_MPRemoteCommandCenter\", \"MediaPlayer_MPRemoteCommandEvent\", \"MediaPlayer_MPSeekCommandEvent\", \"MediaPlayer_MPSkipIntervalCommand\", \"MediaPlayer_MPSkipIntervalCommandEvent\", \"MediaPlayer_all\", \"Metal\", \"MetalFX\", \"MetalFX_MTLFXSpatialScalerDescriptor\", \"MetalFX_MTLFXTemporalScalerDescriptor\", \"MetalFX_all\", \"MetalKit\", \"MetalKit_MTKMesh\", \"MetalKit_MTKMeshBuffer\", \"MetalKit_MTKMeshBufferAllocator\", \"MetalKit_MTKSubmesh\", \"MetalKit_MTKTextureLoader\", \"MetalKit_MTKView\", \"MetalKit_all\", \"Metal_MTLAccelerationStructureBoundingBoxGeometryDescriptor\", \"Metal_MTLAccelerationStructureDescriptor\", \"Metal_MTLAccelerationStructureGeometryDescriptor\", \"Metal_MTLAccelerationStructureMotionBoundingBoxGeometryDescriptor\", \"Metal_MTLAccelerationStructureMotionTriangleGeometryDescriptor\", \"Metal_MTLAccelerationStructurePassDescriptor\", \"Metal_MTLAccelerationStructurePassSampleBufferAttachmentDescriptor\", \"Metal_MTLAccelerationStructurePassSampleBufferAttachmentDescriptorArray\", \"Metal_MTLAccelerationStructureTriangleGeometryDescriptor\", \"Metal_MTLArgument\", \"Metal_MTLArgumentDescriptor\", \"Metal_MTLArrayType\", \"Metal_MTLAttribute\", \"Metal_MTLAttributeDescriptor\", \"Metal_MTLAttributeDescriptorArray\", \"Metal_MTLBinaryArchiveDescriptor\", \"Metal_MTLBlitPassDescriptor\", \"Metal_MTLBlitPassSampleBufferAttachmentDescriptor\", \"Metal_MTLBlitPassSampleBufferAttachmentDescriptorArray\", \"Metal_MTLBufferLayoutDescriptor\", \"Metal_MTLBufferLayoutDescriptorArray\", \"Metal_MTLCaptureDescriptor\", \"Metal_MTLCaptureManager\", \"Metal_MTLCommandBufferDescriptor\", \"Metal_MTLCompileOptions\", \"Metal_MTLComputePassDescriptor\", \"Metal_MTLComputePassSampleBufferAttachmentDescriptor\", \"Metal_MTLComputePassSampleBufferAttachmentDescriptorArray\", \"Metal_MTLComputePipelineDescriptor\", \"Metal_MTLComputePipelineReflection\", \"Metal_MTLCounterSampleBufferDescriptor\", \"Metal_MTLDepthStencilDescriptor\", \"Metal_MTLFunctionConstant\", \"Metal_MTLFunctionConstantValues\", \"Metal_MTLFunctionDescriptor\", \"Metal_MTLFunctionStitchingAttributeAlwaysInline\", \"Metal_MTLFunctionStitchingFunctionNode\", \"Metal_MTLFunctionStitchingGraph\", \"Metal_MTLFunctionStitchingInputNode\", \"Metal_MTLHeapDescriptor\", \"Metal_MTLIOCommandQueueDescriptor\", \"Metal_MTLIndirectCommandBufferDescriptor\", \"Metal_MTLInstanceAccelerationStructureDescriptor\", \"Metal_MTLIntersectionFunctionDescriptor\", \"Metal_MTLIntersectionFunctionTableDescriptor\", \"Metal_MTLLinkedFunctions\", \"Metal_MTLMeshRenderPipelineDescriptor\", \"Metal_MTLMotionKeyframeData\", \"Metal_MTLPipelineBufferDescriptor\", \"Metal_MTLPipelineBufferDescriptorArray\", \"Metal_MTLPointerType\", \"Metal_MTLPrimitiveAccelerationStructureDescriptor\", \"Metal_MTLRasterizationRateLayerArray\", \"Metal_MTLRasterizationRateLayerDescriptor\", \"Metal_MTLRasterizationRateMapDescriptor\", \"Metal_MTLRasterizationRateSampleArray\", \"Metal_MTLRenderPassAttachmentDescriptor\", \"Metal_MTLRenderPassColorAttachmentDescriptor\", \"Metal_MTLRenderPassColorAttachmentDescriptorArray\", \"Metal_MTLRenderPassDepthAttachmentDescriptor\", \"Metal_MTLRenderPassDescriptor\", \"Metal_MTLRenderPassSampleBufferAttachmentDescriptor\", \"Metal_MTLRenderPassSampleBufferAttachmentDescriptorArray\", \"Metal_MTLRenderPassStencilAttachmentDescriptor\", \"Metal_MTLRenderPipelineColorAttachmentDescriptor\", \"Metal_MTLRenderPipelineColorAttachmentDescriptorArray\", \"Metal_MTLRenderPipelineDescriptor\", \"Metal_MTLRenderPipelineFunctionsDescriptor\", \"Metal_MTLRenderPipelineReflection\", \"Metal_MTLResourceStatePassDescriptor\", \"Metal_MTLResourceStatePassSampleBufferAttachmentDescriptor\", \"Metal_MTLResourceStatePassSampleBufferAttachmentDescriptorArray\", \"Metal_MTLSamplerDescriptor\", \"Metal_MTLSharedEventHandle\", \"Metal_MTLSharedEventListener\", \"Metal_MTLSharedTextureHandle\", \"Metal_MTLStageInputOutputDescriptor\", \"Metal_MTLStencilDescriptor\", \"Metal_MTLStitchedLibraryDescriptor\", \"Metal_MTLStructMember\", \"Metal_MTLStructType\", \"Metal_MTLTextureDescriptor\", \"Metal_MTLTextureReferenceType\", \"Metal_MTLTileRenderPipelineColorAttachmentDescriptor\", \"Metal_MTLTileRenderPipelineColorAttachmentDescriptorArray\", \"Metal_MTLTileRenderPipelineDescriptor\", \"Metal_MTLType\", \"Metal_MTLVertexAttribute\", \"Metal_MTLVertexAttributeDescriptor\", \"Metal_MTLVertexAttributeDescriptorArray\", \"Metal_MTLVertexBufferLayoutDescriptor\", \"Metal_MTLVertexBufferLayoutDescriptorArray\", \"Metal_MTLVertexDescriptor\", \"Metal_MTLVisibleFunctionTableDescriptor\", \"Metal_all\", \"MetricKit\", \"MetricKit_MXAnimationMetric\", \"MetricKit_MXAppExitMetric\", \"MetricKit_MXAppLaunchDiagnostic\", \"MetricKit_MXAppLaunchMetric\", \"MetricKit_MXAppResponsivenessMetric\", \"MetricKit_MXAppRunTimeMetric\", \"MetricKit_MXAverage\", \"MetricKit_MXBackgroundExitData\", \"MetricKit_MXCPUExceptionDiagnostic\", \"MetricKit_MXCPUMetric\", \"MetricKit_MXCallStackTree\", \"MetricKit_MXCellularConditionMetric\", \"MetricKit_MXCrashDiagnostic\", \"MetricKit_MXDiagnostic\", \"MetricKit_MXDiagnosticPayload\", \"MetricKit_MXDiskIOMetric\", \"MetricKit_MXDiskWriteExceptionDiagnostic\", \"MetricKit_MXDisplayMetric\", \"MetricKit_MXForegroundExitData\", \"MetricKit_MXGPUMetric\", \"MetricKit_MXHangDiagnostic\", \"MetricKit_MXHistogram\", \"MetricKit_MXHistogramBucket\", \"MetricKit_MXLocationActivityMetric\", \"MetricKit_MXMemoryMetric\", \"MetricKit_MXMetaData\", \"MetricKit_MXMetric\", \"MetricKit_MXMetricManager\", \"MetricKit_MXMetricPayload\", \"MetricKit_MXNetworkTransferMetric\", \"MetricKit_MXSignpostIntervalData\", \"MetricKit_MXSignpostMetric\", \"MetricKit_MXUnitAveragePixelLuminance\", \"MetricKit_MXUnitSignalBars\", \"MetricKit_all\", \"OSAKit\", \"OSAKit_OSALanguage\", \"OSAKit_OSALanguageInstance\", \"OSAKit_OSAScript\", \"OSAKit_OSAScriptController\", \"OSAKit_OSAScriptView\", \"OSAKit_all\", \"PhotoKit\", \"PhotoKit_PHAdjustmentData\", \"PhotoKit_PHAsset\", \"PhotoKit_PHAssetChangeRequest\", \"PhotoKit_PHAssetCollection\", \"PhotoKit_PHAssetCollectionChangeRequest\", \"PhotoKit_PHAssetCreationRequest\", \"PhotoKit_PHAssetResource\", \"PhotoKit_PHAssetResourceCreationOptions\", \"PhotoKit_PHAssetResourceManager\", \"PhotoKit_PHAssetResourceRequestOptions\", \"PhotoKit_PHCachingImageManager\", \"PhotoKit_PHChange\", \"PhotoKit_PHChangeRequest\", \"PhotoKit_PHCloudIdentifier\", \"PhotoKit_PHCloudIdentifierMapping\", \"PhotoKit_PHCollection\", \"PhotoKit_PHCollectionList\", \"PhotoKit_PHCollectionListChangeRequest\", \"PhotoKit_PHContentEditingInput\", \"PhotoKit_PHContentEditingInputRequestOptions\", \"PhotoKit_PHContentEditingOutput\", \"PhotoKit_PHFetchOptions\", \"PhotoKit_PHFetchResult\", \"PhotoKit_PHFetchResultChangeDetails\", \"PhotoKit_PHImageManager\", \"PhotoKit_PHImageRequestOptions\", \"PhotoKit_PHLivePhoto\", \"PhotoKit_PHLivePhotoEditingContext\", \"PhotoKit_PHLivePhotoRequestOptions\", \"PhotoKit_PHLocalIdentifierMapping\", \"PhotoKit_PHObject\", \"PhotoKit_PHObjectChangeDetails\", \"PhotoKit_PHObjectPlaceholder\", \"PhotoKit_PHPersistentChange\", \"PhotoKit_PHPersistentChangeFetchResult\", \"PhotoKit_PHPersistentChangeToken\", \"PhotoKit_PHPersistentObjectChangeDetails\", \"PhotoKit_PHPhotoLibrary\", \"PhotoKit_PHProject\", \"PhotoKit_PHProjectChangeRequest\", \"PhotoKit_PHVideoRequestOptions\", \"PhotoKit_all\", \"SoundAnalysis\", \"SoundAnalysis_SNAudioFileAnalyzer\", \"SoundAnalysis_SNAudioStreamAnalyzer\", \"SoundAnalysis_SNClassification\", \"SoundAnalysis_SNClassificationResult\", \"SoundAnalysis_SNClassifySoundRequest\", \"SoundAnalysis_SNTimeDurationConstraint\", \"SoundAnalysis_all\", \"Speech\", \"Speech_SFAcousticFeature\", \"Speech_SFSpeechAudioBufferRecognitionRequest\", \"Speech_SFSpeechRecognitionMetadata\", \"Speech_SFSpeechRecognitionRequest\", \"Speech_SFSpeechRecognitionResult\", \"Speech_SFSpeechRecognitionTask\", \"Speech_SFSpeechRecognizer\", \"Speech_SFSpeechURLRecognitionRequest\", \"Speech_SFTranscription\", \"Speech_SFTranscriptionSegment\", \"Speech_SFVoiceAnalytics\", \"Speech_all\", \"StoreKit\", \"StoreKit_SKAdImpression\", \"StoreKit_SKAdNetwork\", \"StoreKit_SKArcadeService\", \"StoreKit_SKCloudServiceController\", \"StoreKit_SKCloudServiceSetupViewController\", \"StoreKit_SKDownload\", \"StoreKit_SKMutablePayment\", \"StoreKit_SKOverlay\", \"StoreKit_SKOverlayAppClipConfiguration\", \"StoreKit_SKOverlayAppConfiguration\", \"StoreKit_SKOverlayConfiguration\", \"StoreKit_SKOverlayTransitionContext\", \"StoreKit_SKPayment\", \"StoreKit_SKPaymentDiscount\", \"StoreKit_SKPaymentQueue\", \"StoreKit_SKPaymentTransaction\", \"StoreKit_SKProduct\", \"StoreKit_SKProductDiscount\", \"StoreKit_SKProductStorePromotionController\", \"StoreKit_SKProductSubscriptionPeriod\", \"StoreKit_SKProductsRequest\", \"StoreKit_SKProductsResponse\", \"StoreKit_SKReceiptRefreshRequest\", \"StoreKit_SKRequest\", \"StoreKit_SKStoreProductViewController\", \"StoreKit_SKStoreReviewController\", \"StoreKit_SKStorefront\", \"StoreKit_all\", \"UniformTypeIdentifiers\", \"UniformTypeIdentifiers_UTType\", \"UniformTypeIdentifiers_all\", \"UserNotifications\", \"UserNotifications_UNCalendarNotificationTrigger\", \"UserNotifications_UNLocationNotificationTrigger\", \"UserNotifications_UNMutableNotificationContent\", \"UserNotifications_UNNotification\", \"UserNotifications_UNNotificationAction\", \"UserNotifications_UNNotificationActionIcon\", \"UserNotifications_UNNotificationAttachment\", \"UserNotifications_UNNotificationCategory\", \"UserNotifications_UNNotificationContent\", \"UserNotifications_UNNotificationRequest\", \"UserNotifications_UNNotificationResponse\", \"UserNotifications_UNNotificationServiceExtension\", \"UserNotifications_UNNotificationSettings\", \"UserNotifications_UNNotificationSound\", \"UserNotifications_UNNotificationTrigger\", \"UserNotifications_UNPushNotificationTrigger\", \"UserNotifications_UNTextInputNotificationAction\", \"UserNotifications_UNTextInputNotificationResponse\", \"UserNotifications_UNTimeIntervalNotificationTrigger\", \"UserNotifications_UNUserNotificationCenter\", \"UserNotifications_all\", \"WebKit\", \"WebKit_DOMAbstractView\", \"WebKit_DOMAttr\", \"WebKit_DOMBlob\", \"WebKit_DOMCDATASection\", \"WebKit_DOMCSSCharsetRule\", \"WebKit_DOMCSSFontFaceRule\", \"WebKit_DOMCSSImportRule\", \"WebKit_DOMCSSMediaRule\", \"WebKit_DOMCSSPageRule\", \"WebKit_DOMCSSPrimitiveValue\", \"WebKit_DOMCSSRule\", \"WebKit_DOMCSSRuleList\", \"WebKit_DOMCSSStyleDeclaration\", \"WebKit_DOMCSSStyleRule\", \"WebKit_DOMCSSStyleSheet\", \"WebKit_DOMCSSUnknownRule\", \"WebKit_DOMCSSValue\", \"WebKit_DOMCSSValueList\", \"WebKit_DOMCharacterData\", \"WebKit_DOMComment\", \"WebKit_DOMCounter\", \"WebKit_DOMDocument\", \"WebKit_DOMDocumentFragment\", \"WebKit_DOMDocumentType\", \"WebKit_DOMElement\", \"WebKit_DOMEntity\", \"WebKit_DOMEntityReference\", \"WebKit_DOMEvent\", \"WebKit_DOMFile\", \"WebKit_DOMFileList\", \"WebKit_DOMHTMLAnchorElement\", \"WebKit_DOMHTMLAppletElement\", \"WebKit_DOMHTMLAreaElement\", \"WebKit_DOMHTMLBRElement\", \"WebKit_DOMHTMLBaseElement\", \"WebKit_DOMHTMLBaseFontElement\", \"WebKit_DOMHTMLBodyElement\", \"WebKit_DOMHTMLButtonElement\", \"WebKit_DOMHTMLCollection\", \"WebKit_DOMHTMLDListElement\", \"WebKit_DOMHTMLDirectoryElement\", \"WebKit_DOMHTMLDivElement\", \"WebKit_DOMHTMLDocument\", \"WebKit_DOMHTMLElement\", \"WebKit_DOMHTMLEmbedElement\", \"WebKit_DOMHTMLFieldSetElement\", \"WebKit_DOMHTMLFontElement\", \"WebKit_DOMHTMLFormElement\", \"WebKit_DOMHTMLFrameElement\", \"WebKit_DOMHTMLFrameSetElement\", \"WebKit_DOMHTMLHRElement\", \"WebKit_DOMHTMLHeadElement\", \"WebKit_DOMHTMLHeadingElement\", \"WebKit_DOMHTMLHtmlElement\", \"WebKit_DOMHTMLIFrameElement\", \"WebKit_DOMHTMLImageElement\", \"WebKit_DOMHTMLInputElement\", \"WebKit_DOMHTMLLIElement\", \"WebKit_DOMHTMLLabelElement\", \"WebKit_DOMHTMLLegendElement\", \"WebKit_DOMHTMLLinkElement\", \"WebKit_DOMHTMLMapElement\", \"WebKit_DOMHTMLMarqueeElement\", \"WebKit_DOMHTMLMenuElement\", \"WebKit_DOMHTMLMetaElement\", \"WebKit_DOMHTMLModElement\", \"WebKit_DOMHTMLOListElement\", \"WebKit_DOMHTMLObjectElement\", \"WebKit_DOMHTMLOptGroupElement\", \"WebKit_DOMHTMLOptionElement\", \"WebKit_DOMHTMLOptionsCollection\", \"WebKit_DOMHTMLParagraphElement\", \"WebKit_DOMHTMLParamElement\", \"WebKit_DOMHTMLPreElement\", \"WebKit_DOMHTMLQuoteElement\", \"WebKit_DOMHTMLScriptElement\", \"WebKit_DOMHTMLSelectElement\", \"WebKit_DOMHTMLStyleElement\", \"WebKit_DOMHTMLTableCaptionElement\", \"WebKit_DOMHTMLTableCellElement\", \"WebKit_DOMHTMLTableColElement\", \"WebKit_DOMHTMLTableElement\", \"WebKit_DOMHTMLTableRowElement\", \"WebKit_DOMHTMLTableSectionElement\", \"WebKit_DOMHTMLTextAreaElement\", \"WebKit_DOMHTMLTitleElement\", \"WebKit_DOMHTMLUListElement\", \"WebKit_DOMImplementation\", \"WebKit_DOMKeyboardEvent\", \"WebKit_DOMMediaList\", \"WebKit_DOMMouseEvent\", \"WebKit_DOMMutationEvent\", \"WebKit_DOMNamedNodeMap\", \"WebKit_DOMNode\", \"WebKit_DOMNodeIterator\", \"WebKit_DOMNodeList\", \"WebKit_DOMObject\", \"WebKit_DOMOverflowEvent\", \"WebKit_DOMProcessingInstruction\", \"WebKit_DOMProgressEvent\", \"WebKit_DOMRGBColor\", \"WebKit_DOMRange\", \"WebKit_DOMRect\", \"WebKit_DOMStyleSheet\", \"WebKit_DOMStyleSheetList\", \"WebKit_DOMText\", \"WebKit_DOMTreeWalker\", \"WebKit_DOMUIEvent\", \"WebKit_DOMWheelEvent\", \"WebKit_DOMXPathExpression\", \"WebKit_DOMXPathResult\", \"WebKit_WKBackForwardList\", \"WebKit_WKBackForwardListItem\", \"WebKit_WKContentRuleList\", \"WebKit_WKContentRuleListStore\", \"WebKit_WKContentWorld\", \"WebKit_WKDownload\", \"WebKit_WKFindConfiguration\", \"WebKit_WKFindResult\", \"WebKit_WKFrameInfo\", \"WebKit_WKHTTPCookieStore\", \"WebKit_WKNavigation\", \"WebKit_WKNavigationAction\", \"WebKit_WKNavigationResponse\", \"WebKit_WKOpenPanelParameters\", \"WebKit_WKPDFConfiguration\", \"WebKit_WKPreferences\", \"WebKit_WKProcessPool\", \"WebKit_WKScriptMessage\", \"WebKit_WKSecurityOrigin\", \"WebKit_WKSnapshotConfiguration\", \"WebKit_WKUserContentController\", \"WebKit_WKUserScript\", \"WebKit_WKWebView\", \"WebKit_WKWebViewConfiguration\", \"WebKit_WKWebpagePreferences\", \"WebKit_WKWebsiteDataRecord\", \"WebKit_WKWebsiteDataStore\", \"WebKit_WKWindowFeatures\", \"WebKit_WebArchive\", \"WebKit_WebBackForwardList\", \"WebKit_WebDataSource\", \"WebKit_WebDownload\", \"WebKit_WebFrame\", \"WebKit_WebFrameView\", \"WebKit_WebHistory\", \"WebKit_WebHistoryItem\", \"WebKit_WebPreferences\", \"WebKit_WebResource\", \"WebKit_WebScriptObject\", \"WebKit_WebUndefined\", \"WebKit_WebView\", \"WebKit_all\", \"alloc\", \"apple\", \"block\", \"block2\", \"default\", \"dispatch\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"objc2\", \"objective-c\", \"std\", \"unstable-docsrs\", \"unstable-example-basic_usage\", \"unstable-example-browser\", \"unstable-example-delegate\", \"unstable-example-nspasteboard\", \"unstable-example-speech_synthesis\", \"unstable-frameworks-all\", \"unstable-frameworks-gnustep\", \"unstable-frameworks-gnustep-32bit\", \"unstable-frameworks-ios\", \"unstable-frameworks-macos-10-13\", \"unstable-frameworks-macos-10-7\", \"unstable-frameworks-macos-11\", \"unstable-frameworks-macos-12\", \"unstable-frameworks-macos-13\", \"unstable-private\", \"unstable-static-nsstring\"]", "target": 11029971253207584918, "profile": 8276155916380437441, "path": 10077126802006468561, "deps": [[3188019848207208646, "block2", false, 5862082413897926005], [12444794977888551920, "objc2", false, 16739452810951144290], [16431863889745914764, "dispatch", false, 17820855263313576664]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/icrate-972913bfcf164b3d/dep-lib-icrate", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}