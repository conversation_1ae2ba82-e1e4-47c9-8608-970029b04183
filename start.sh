#!/bin/bash

echo "=== Vulkan Triangle Quick Start ==="
echo ""

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Функция для проверки команды
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "${GREEN}✓${NC} $2 найден"
        return 0
    else
        echo -e "${RED}✗${NC} $2 не найден"
        return 1
    fi
}

# Функция для проверки файла
check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✓${NC} $2 найден"
        return 0
    else
        echo -e "${RED}✗${NC} $2 не найден"
        return 1
    fi
}

# Проверка основных зависимостей
echo "Проверка зависимостей:"
check_command "brew" "Homebrew"
check_command "cmake" "CMake"
check_command "make" "Make"
check_command "clang++" "Clang++"

echo ""

# Установка недостающих компонентов
if ! command -v brew &> /dev/null; then
    echo -e "${YELLOW}Установка Homebrew...${NC}"
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
fi

# Установка GLFW если не установлен
if ! brew list glfw &>/dev/null; then
    echo -e "${YELLOW}Установка GLFW...${NC}"
    brew install glfw
fi

# Проверка Vulkan SDK
echo "Поиск Vulkan SDK..."
VULKAN_SDK_PATHS=(
    "$HOME/VulkanSDK/*/macOS"
    "/usr/local/VulkanSDK/*/macOS"
    "/opt/homebrew/Caskroom/vulkan-sdk/*/macOS"
    "/usr/local/Caskroom/vulkan-sdk/*/macOS"
)

VULKAN_SDK=""
for path in "${VULKAN_SDK_PATHS[@]}"; do
    if compgen -G "$path" > /dev/null 2>&1; then
        VULKAN_SDK=$(ls -d $path 2>/dev/null | head -n 1)
        break
    fi
done

if [ -z "$VULKAN_SDK" ]; then
    echo -e "${RED}Vulkan SDK не найден!${NC}"
    echo ""
    echo "Установка Vulkan SDK через Homebrew..."
    brew install --cask vulkan-sdk
    
    # Повторный поиск после установки
    for path in "${VULKAN_SDK_PATHS[@]}"; do
        if compgen -G "$path" > /dev/null 2>&1; then
            VULKAN_SDK=$(ls -d $path 2>/dev/null | head -n 1)
            break
        fi
    done
fi

if [ -n "$VULKAN_SDK" ]; then
    echo -e "${GREEN}✓${NC} Vulkan SDK найден: $VULKAN_SDK"
    export VULKAN_SDK
    export PATH="$VULKAN_SDK/bin:$PATH"
else
    echo -e "${RED}Не удалось найти или установить Vulkan SDK${NC}"
    echo "Скачайте и установите вручную с: https://vulkan.lunarg.com/sdk/home#mac"
    exit 1
fi

echo ""

# Проверка исходных файлов
echo "Проверка файлов проекта:"
check_file "main.cpp" "main.cpp"
check_file "Makefile" "Makefile"

if [ ! -f "main.cpp" ] || [ ! -f "Makefile" ]; then
    echo -e "${RED}Необходимые файлы проекта отсутствуют!${NC}"
    echo "Создайте файлы согласно инструкции"
    exit 1
fi

# Проверка/создание шейдеров
echo ""
echo "Проверка шейдеров:"
if [ ! -d "shaders" ]; then
    mkdir shaders
fi

if [ ! -f "shaders/shader.vert" ]; then
    echo "Создание вершинного шейдера..."
    cat > shaders/shader.vert << 'EOF'
#version 450

vec2 positions[3] = vec2[](
    vec2(0.0, -0.5),
    vec2(0.5, 0.5),
    vec2(-0.5, 0.5)
);

vec3 colors[3] = vec3[](
    vec3(1.0, 0.0, 0.0),
    vec3(0.0, 1.0, 0.0),
    vec3(0.0, 0.0, 1.0)
);

layout(location = 0) out vec3 fragColor;

void main() {
    gl_Position = vec4(positions[gl_VertexIndex], 0.0, 1.0);
    fragColor = colors[gl_VertexIndex];
}
EOF
fi

if [ ! -f "shaders/shader.frag" ]; then
    echo "Создание фрагментного шейдера..."
    cat > shaders/shader.frag << 'EOF'
#version 450

layout(location = 0) in vec3 fragColor;
layout(location = 0) out vec4 outColor;

void main() {
    outColor = vec4(fragColor, 1.0);
}
EOF
fi

check_file "shaders/shader.vert" "Вершинный шейдер"
check_file "shaders/shader.frag" "Фрагментный шейдер"

# Компиляция и запуск
echo ""
echo -e "${YELLOW}Компиляция проекта...${NC}"
echo ""

# Использование Makefile с передачей VULKAN_SDK
if VULKAN_SDK="$VULKAN_SDK" make; then
    echo ""
    echo -e "${GREEN}Компиляция успешна!${NC}"
    echo ""
    echo -e "${YELLOW}Запуск приложения...${NC}"
    echo ""
    ./VulkanTriangle
else
    echo ""
    echo -e "${RED}Ошибка компиляции!${NC}"
    echo ""
    echo "Возможные причины:"
    echo "1. Vulkan SDK не установлен правильно"
    echo "2. GLFW не установлен"
    echo "3. Ошибки в коде"
    echo ""
    echo "Попробуйте:"
    echo "  make check  # для проверки окружения"
    exit 1
fi