# Makefile для Vulkan Triangle на macOS ARM64

# Компилятор
CXX = clang++

# Флаги компиляции
CXXFLAGS = -std=c++17 -O2 -Wall -Wextra

# Определение путей для ARM64 и Intel
UNAME_M := $(shell uname -m)
ifeq ($(UNAME_M),arm64)
    HOMEBREW_PREFIX = /opt/homebrew
else
    HOMEBREW_PREFIX = /usr/local
endif

# Автоматический поиск Vulkan SDK
VULKAN_SDK ?= $(shell ls -d ~/VulkanSDK/*/macOS 2>/dev/null | head -n 1)
ifeq ($(VULKAN_SDK),)
    VULKAN_SDK = $(shell ls -d /usr/local/VulkanSDK/*/macOS 2>/dev/null | head -n 1)
endif
ifeq ($(VULKAN_SDK),)
    VULKAN_SDK = $(shell ls -d $(HOMEBREW_PREFIX)/Caskroom/vulkan-sdk/*/macOS 2>/dev/null | head -n 1)
endif

# Проверка наличия Vulkan SDK
ifeq ($(VULKAN_SDK),)
    $(error Vulkan SDK не найден. Установите его и укажите путь: make VULKAN_SDK=/path/to/vulkan/sdk)
endif

# Путь к GLFW
GLFW_PATH = $(HOMEBREW_PREFIX)

# Включаемые директории
INCLUDES = -I$(VULKAN_SDK)/include \
           -I$(GLFW_PATH)/include

# Библиотеки
LIBS = -L$(VULKAN_SDK)/lib -lvulkan \
       -L$(GLFW_PATH)/lib -lglfw \
       -framework Cocoa \
       -framework IOKit \
       -framework CoreVideo

# Исполняемый файл
TARGET = VulkanTriangle

# Исходные файлы
SOURCES = main.cpp

# Объектные файлы
OBJECTS = $(SOURCES:.cpp=.o)

# Правило по умолчанию
all: shaders $(TARGET)

# Компиляция шейдеров
shaders:
	@echo "Компиляция шейдеров..."
	@mkdir -p shaders
	@cd shaders && \
	if [ ! -f vert.spv ] || [ ! -f frag.spv ]; then \
		$(VULKAN_SDK)/bin/glslc shader.vert -o vert.spv && \
		$(VULKAN_SDK)/bin/glslc shader.frag -o frag.spv && \
		echo "Шейдеры скомпилированы"; \
	else \
		echo "Шейдеры уже скомпилированы"; \
	fi

# Линковка
$(TARGET): $(OBJECTS)
	@echo "Линковка $(TARGET)..."
	$(CXX) $(OBJECTS) $(LIBS) -o $(TARGET)
	@echo "Сборка завершена!"

# Компиляция исходных файлов
%.o: %.cpp
	@echo "Компиляция $<..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Очистка
clean:
	rm -f $(OBJECTS) $(TARGET)
	rm -f shaders/*.spv

# Полная очистка
distclean: clean
	rm -rf build/

# Запуск
run: all
	./$(TARGET)

# Проверка окружения
check:
	@echo "Проверка окружения..."
	@echo "Архитектура: $(UNAME_M)"
	@echo "Homebrew prefix: $(HOMEBREW_PREFIX)"
	@echo "Vulkan SDK: $(VULKAN_SDK)"
	@echo -n "GLFW: "
	@if [ -d "$(GLFW_PATH)/lib" ]; then echo "найден"; else echo "не найден"; fi
	@echo -n "glslc: "
	@if [ -f "$(VULKAN_SDK)/bin/glslc" ]; then echo "найден"; else echo "не найден"; fi
	@echo ""
	@echo "Если Vulkan SDK не найден, установите его:"
	@echo "  brew install --cask vulkan-sdk"
	@echo "или скачайте с https://vulkan.lunarg.com/sdk/home#mac"

.PHONY: all shaders clean distclean run check