# Vulkan Triangle: Обычный Vulkan vs vkBootstrap

Этот проект демонстрирует две реализации простого Vulkan приложения, рисующего треугольник:
1. **Полная ручная реализация** с использованием чистого Vulkan API
2. **Упрощенная версия** с использованием библиотеки vkBootstrap

## Требования

### Системные требования:
- macOS с поддержкой Vulkan (через MoltenVK)
- Vulkan SDK 1.4.313.0 или новее
- GLFW 3.x
- Clang++ с поддержкой C++17

### Установка зависимостей:

1. **Vulkan SDK:**
   ```bash
   # Скачайте и установите с https://vulkan.lunarg.com/
   export VULKAN_SDK=/Users/<USER>/VulkanSDK/1.4.313.0/macOS
   ```

2. **GLFW:**
   ```bash
   brew install glfw
   ```

3. **vkBootstrap (для второй версии):**
   ```bash
   git submodule add https://github.com/charles-lunarg/vk-bootstrap.git vk-bootstrap
   git submodule update --init --recursive
   ```

## Сборка и запуск

### Версия 1: Обычный Vulkan

```bash
# Сборка
make

# Запуск
./VulkanTriangle
```

### Версия 2: vkBootstrap

```bash
# Сборка
make -f Makefile.vkbootstrap

# Запуск
./VulkanTriangleBootstrap
```

### Версия 3: Rust (демо)

```bash
cd rust-version

# Сборка (требует доработки)
cargo build

# Примечание: неполная реализация
```

### Версия 4: Zig (демо)

```bash
cd zig-version

# Сборка
zig build

# Запуск (базовая инициализация)
zig build run
```

## Исправленные проблемы

### Проблема с семафорами
Оба приложения включают исправление критической ошибки синхронизации:

**Проблема:** Validation layer ошибки о повторном использовании семафоров
```
validation layer: vkQueueSubmit(): pSubmits[0].pSignalSemaphores[0] is being signaled
but it may still be in use by VkSwapchainKHR
```

**Решение:** Использование отдельных семафоров для каждого изображения swapchain:
- Семафоры индексируются по номеру изображения swapchain, а не по номеру кадра
- Заборы (fences) остаются привязанными к кадрам для CPU-GPU синхронизации

## Ключевые различия

| Аспект | Обычный Vulkan | vkBootstrap |
|--------|----------------|-------------|
| Строки кода | ~904 | ~575 |
| Функций инициализации | 15+ | 8 |
| Validation layers | Ручная настройка | Автоматическая |
| Выбор устройства | Ручная проверка | Автоматический |
| Создание swapchain | Много boilerplate | Простой builder |
| Обработка ошибок | Ручная | Встроенная |

Подробное сравнение смотрите в [COMPARISON.md](COMPARISON.md)