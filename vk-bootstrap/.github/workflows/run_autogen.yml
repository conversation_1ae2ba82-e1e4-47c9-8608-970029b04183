# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the “Software”), to deal in the Software without restriction, including without
# limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
# of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
# IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
#
# Copyright © 2023 <PERSON> (<EMAIL>)
#

# The purpose of this script is to automatically run the autogen code every week and submit a PR to include the changes

name: Run autogen

on:
  schedule:
    - cron: '0 0 * * 2'
  workflow_dispatch:

jobs:
  run_autogen:
    name: Run autogen
    runs-on: ubuntu-latest
    env:
        PR_NUMBER: ${{ github.event.number }}

    steps:
      - uses: actions/create-github-app-token@v2
        id: generate-token
        with:
          app-id: ${{ vars.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
            python-version: '3.9'

      - name: Install python package 'xmltodict'
        run: pip install xmltodict

      - name: Check out repository code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Run dispatch generator
        run: python script/generate_dispatch.py --auto

      - name: Diff source to see if anything changed
        id: git-diff
        run: echo "::set-output name=git-diff::$(git diff --quiet HEAD~0 || echo true)"

      - name: pull-request
        uses: peter-evans/create-pull-request@v7
        if: ${{ steps.git-diff.outputs.git-diff == 'true' }}
        with:
          token: ${{ steps.generate-token.outputs.token }}
          commit-message: Update to latest Vulkan-Headers
          title: Update to latest Vulkan-Headers
          branch: run-autogen
          base: main
          delete-branch: true
