#!/usr/bin/env python3

# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the “Software”), to deal in the Software without restriction, including without
# limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
# of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
# IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
#
# Copyright © 2023 <PERSON> (<EMAIL>)
#

name: Tag header update

on:
  workflow_dispatch:

jobs:
  tag_header_update:
    name: Tag header update
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Read CurrentBuildVulkanVersion.cmake
        id: read-version-file
        uses: juliangruber/read-file-action@v1
        with:
          path: gen/CurrentBuildVulkanVersion.cmake

      - name: Match regex from version file
        uses: actions-ecosystem/action-regex-match@v2
        id: regex-match
        with:
          text: ${{ steps.read-version-file.outputs.content }}
          regex: 'VK_BOOTSTRAP_SOURCE_HEADER_VERSION_GIT_TAG (v[0-9]\.[0-9]\.[0-9]*)'

      - name: Push tag
        uses: EndBug/latest-tag@latest
        if: ${{ steps.regex-match.outputs.match != '' }}
        with:
            tag-name: ${{ steps.regex-match.outputs.group1 }}
            description: Update to ${{ steps.regex-match.outputs.group1 }} of Vulkan-Headers

