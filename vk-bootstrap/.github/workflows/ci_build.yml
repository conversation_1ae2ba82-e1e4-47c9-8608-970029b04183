#!/usr/bin/env python3

# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the “Software”), to deal in the Software without restriction, including without
# limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
# of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
# IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
#
# Copyright © 2023 <PERSON> (<EMAIL>)
#


name: CI Build

on: [push, pull_request]

jobs:
    linux:
        name: Linux
        runs-on: ubuntu-latest
        if: github.event_name != 'pull_request' || github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name
        strategy:
            fail-fast: false
            matrix:
                type: ["Debug", "Release"]
                cc: ["gcc", "clang"]
                cxx: ["g++", "clang++"]
                exclude:
                    - cc: gcc
                      cxx: clang++
                    - cc: clang
                      cxx: g++


        steps:
            - uses: actions/checkout@v4
            - uses: lukka/get-cmake@latest
              with:
                cmakeVersion: 3.22

            - name: Install build dependencies
              run: |
                sudo apt-get update
                sudo apt-get install -y xorg-dev

            - name: CMake Configure
              run: cmake -S. -B build -DCMAKE_BUILD_TYPE=${{matrix.type}} -DVK_BOOTSTRAP_WERROR=ON -DVK_BOOTSTRAP_TEST=ON -DENABLE_ADDRESS_SANITIZER=ON

            - name: CMake Build
              run: cmake --build build

            - name: Install
              run: cmake --install build --prefix build/install

            - name: Run tests
              working-directory: ./build
              run: ctest --output-on-failure -C ${{matrix.type}}

    windows:
        name: Windows
        runs-on: windows-latest
        if: github.event_name != 'pull_request' || github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name
        strategy:
            matrix:
                arch: [ Win32, x64 ]
                config: [ Debug, Release ]

        steps:
            - uses: actions/checkout@v4

            - name: CMake Configure
              run: cmake -S. -B build -D CMAKE_BUILD_TYPE=${{matrix.config}} -D VK_BOOTSTRAP_WERROR=ON -D VK_BOOTSTRAP_TEST=ON -A ${{matrix.arch}}

            - name: CMake Build
              run: cmake --build build

            - name: Run  tests
              working-directory: ./build
              run: ctest --output-on-failure -C ${{matrix.config}}
